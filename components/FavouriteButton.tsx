'use client';

import React, { useCallback } from 'react';

import useFavouriteStore from '@/store/favourite';
import classNames from 'classnames';
import useFavourite from 'hooks/Ent/useFavourite';

interface FavouriteItem {
  item: string;
  object_id: number;
}

interface FavouriteButtonProps {
  favouriteList: FavouriteItem[] | [];
  item: string;
  object_id: number;
  className?: string; // <-- thêm ở đây
}

const FavouriteButton: React.FC<FavouriteButtonProps> = ({
  favouriteList,
  item,
  object_id,
  className, // <-- destructure ở đây
}) => {
  const { addFavourite, removeFavourite } = useFavouriteStore();
  const { saveFavourite } = useFavourite();

  const isToggled = favouriteList.some(
    (fav: any) => fav.object_id === object_id && fav.item === item
  );

  const handleToggleFavourite = useCallback(async () => {
    if (isToggled) {
      removeFavourite({ item, object_id });
    } else {
      addFavourite({ item, object_id });
    }

    try {
      await saveFavourite(isToggled ? 1 : 2, item, object_id);
    } catch (error) {
      console.error('Lỗi khi cập nhật favourite:', error);
      if (isToggled) {
        addFavourite({ item, object_id });
      } else {
        removeFavourite({ item, object_id });
      }
    }
  }, [isToggled, addFavourite, removeFavourite, item, object_id, saveFavourite]);

  return (
    <div className={classNames({ 'group-hover:flex hidden': !isToggled })}>
      <div
        onClick={handleToggleFavourite}
        className={classNames(
          'flex items-center justify-center w-[28px] h-[27px] rounded-md group-hover:bg-bg-box cursor-pointer',
          className // <-- truyền className nếu có
        )}
      >
        <div
          className={classNames('text-[15px] group-hover:bg-grey-300 transition-all duration-200', {
            'icon-star-line fill-color-minor': !isToggled,
            'icon-star-fill text-yellow-100': isToggled,
          })}
        ></div>
      </div>
    </div>
  );
};

export default FavouriteButton;
