export interface FetchDataResponse {
  success: boolean;
  data: any;
  message: string;
}

export type Entity = {
  id: number;
  title: string;
};

export interface CategoryEntity extends Entity {
  keyx: string;
  status: number;
  title_vi: string;
  is_favourite: number;
  parent_id: number | null;
  icon: string | null;
  valuex: string | number | boolean;
  items: Array<CategoryEntity>;
}
export interface CategoryMemberEntity extends CategoryEntity {
  category_id: number;
}

export interface CourseEntity {
  status: number;
  title: string;
  id: number;
  documents?: Array<DocumentEntity>;
  title_vi: string;
}

export interface DocumentEntity extends Entity {
  keyx: string;
  status: number;
  course_id: number;
  paragraphs: Array<ParagraphEntity>;
  course: CourseEntity;
}

export interface MemberEntity {
  id: number;
  token: string;
  fullname: string;
  nickname: string;
  avatar: string;
  password: string;
  status: number;
  is_main: number;
  is_onboard: number;
  account_id: number;
  phone?: string;
  member_token?: { quantity?: number };
}

export interface ParagraphEntity extends Entity {
  keyx: string;
  status: number;
  document_id: number;
  approve_id?: number;
  document: DocumentEntity;
  type: number;
  position: number;
  description?: string;
  account_id: number;
  course_id: number;
  image?: string;
  process_approve?: number;
  is_favourite?: number;
  item: string;
  id: number;
  title: string;
  ParagraphCurrentTitle: string;
  ParagraphCurrentKeyx: string;
  paragraph_current_id: number;
  document_current_id: number;
  course_current_id: number;
  title_vi?: string;
  course?: CourseEntity;
  description_vi?: string;
  object_id?: number;
}

export interface TranslationItem {
  id: number;
  item: string;
  object_id: number;
  content: string;
  translate_google: string;
  lang_from: string;
  lang_to: string;
  status: number;
  created_at: number;
}

export interface AudioEntity {
  id: number;
  sentence_id: number;
  voice_id: number;
  transcription_id: string;
  accent_id: number;
  duration: number;
  url: string;
  length: number;
  process: number;
  created: number;
  status: number;
}

export interface SentenceScoreEntity {
  character_id: number;
  color: string;
  content: string;
  id: number;
  member_id: number;
  paragraph_id: number;
  score: number;
  sentence_id: number;
  member_token: {
    quantity: number;
  };
}

export interface SentenceEntity {
  status: number;
  document_id: number;
  start?: number;
  end?: number;
  id: number;
  course_id: number;
  paragraph_id: number;
  character_id: number;
  content: string;
  words: string;
  words_arr: string[];
  process_audio: number;
  process_content: number;
  score?: number;
  color?: string;
  group?: number;
  created_at?: number;
  translation?: TranslationItem | null;
  audios?: Array<AudioEntity> | null;
  sentence_group_id?: number;
  position?: number;
}

export interface DictionaryWord {
  id: number;
  word_id: number;
  pos: string;
  pos_group: string;
  pos_ti: string;
  pos_full: string;
  lemma: string;
  word: string;
  phonetic: string;
  translate: string;
  definition: string;
  definition_vi: string;
  process_pos: number;
  status: number;
  title: string;
  title_en: string;
  example_vi: string;
  example: string;
}

export interface SentenceDetail {
  id: number;
  lemma: string;
  pos: string;
  pos_group: string;
  sentence_id: number;
  word: string;
  position?: number;
  status: number;
}

export interface SentencePhrase {
  id: number;
  component: string;
  sentence_id: number;
  content: string;
  translation: string;
  position?: number;
  status: number;
  head: string;
  label: string;
  label_detail: {
    description_en: string;
    description_vi: string;
    example: string;
    explain_en: string;
    explain_vi: string;
    id: number;
    keyx: string;
    title_en: string;
    title_vi: string;
  };
  words: string;
}

export interface DictionaryEntity {
  id: number;
  tag_bc: string;
  tag_un: string;
  tag_ti: string;
  title_en: string;
  title: string;
  process: number;
  audio: string;
  means: Array<DictionaryWord>;
}

export interface Character {
  id: number;
  voice_id: number;
  fullname: string;
  description: string;
  sentence_current_id: number;
  course_id: number;
  age: string;
  gender: string;
  accent: string;
}
export interface PaginatedResponse {
  data: FetchDataResponse;
  total: number;
}
export interface Pagination {
  total: number;
  page: number;
  last_page: number;
  hasMore: boolean;
}

export interface HistorySentenceEntity {
  id: number;
  sentence_id: number;
  created_at?: number;
  sentence?: SentenceEntity | null;
  audios?: Array<AudioEntity> | null;
  member_id: number;
  updated_at?: number;
}

export interface HistoryWordEntity {
  id: number;
  sentence_id: number;
  created_at?: number;
  sentence: SentenceEntity | null;
  audios?: Array<AudioEntity> | null;
  position: number | null;
  member_id: number;
  sentence_pos: SentencePosEntity;
  updated_at?: number;
}

export interface HistoryParagraphEntity {
  id: number;
  course_id: number;
  created_at?: number;
  sentence: Array<SentenceEntity> | null;
  member_id: number;
  document_id: number;
  paragraph: ParagraphEntity;
  course: CourseEntity;
  title: string | null;
  type: number;
  updated_at?: number;
}

export interface AccountEntity {
  created_at: number;
  fullname?: string;
  id?: number;
  phone?: string;
  status?: number;
  role_id?: number;
}

export interface AccountGroupsEntity {
  account_id: number;
  group_id?: number;
  id?: number;
  role_id?: number;
  status?: number;
  account?: AccountEntity;
}

export interface MemberGroupsEntity {
  member_id: number;
  group_id?: number;
  id: number;
  approve_process?: number;
  status?: number;
  member?: MemberEntity;
}

export interface GroupsEntity {
  id: number;
  parent_id: number;
  city_id?: number;
  district_id?: number;
  ward_id?: number;
  status: number;
  type: number;
  title: string;
  parent_title?: string;
  address: string | null;
  token: string | null;
  account_groups?: AccountGroupsEntity;
  member_groups?: AccountGroupsEntity;
}

export interface SentencePosEntity {
  id: number;
  sentence_id: number;
  word: string;
  lemma: string;
  pos: string;
  status: number;
  word_type: string;
  audio: string;
  phonetic: string;
}

export interface FavouriteEntity {
  item: string;
  member_id: number;
  id: number;
  object_id: number;
  updated_at: number;
  paragraph?: ParagraphEntity;
}

export interface PronounceEntity {
  id: number;
  word: string;
  type: number;
  guide: string;
  examples: string;
  url: string;
  phonetics: Array<PhoneticEntity> | null;
}

export interface PhoneticEntity {
  id: number;
  word_id: number;
  word: string;
  text: string;
  audio: string;
}

export interface KnowledgeEntity {
  id: number;
  partner_id: number;
  limit: number;
  type: number;
  status: number;
  title: string;
  keywords: string;
  definition: string;
  definition_vi: string;
  children: KnowledgeEntity[];
}

export interface TagDetailEntity extends Entity {
  item: string;
  tag_id: number;
  object_id: number;
  status: number;
  document?: DocumentEntity;
  course?: CourseEntity;
  paragraph?: ParagraphEntity;
}

export interface StartParagraphApiProps {
  document_id: number;
  course_id: number;
  paragraph_id: number;
  activeTab?: string;
  member_exercise_token?: string;
}

export interface EndParagraphApiProps {
  document_id: number;
  course_id: number;
  paragraph_id: number;
  member_id: number;
  member_exercise_token?: string;
}

export interface TransactionLearnInfo {
  id: number;
  member_id: number;
  process: number;
  start_at: number;
  status: number;
  type: number;
}

export interface QuizzQuestion {
  id: number;
  quiz_id: number;
  content: string;
  translate: string;
  status: number;
  type: number;
}

export interface TagsEntity extends Entity {
  item: string;
  tag_id: number;
  object_id: number;
  status: number;
  document?: DocumentEntity;
  course?: CourseEntity;
  paragraph?: ParagraphEntity;
  parent_id?: number;
  keyx?: string;
}
export interface QuizzAnswers {
  id: number;
  question_id: number;
  content: string;
  type: number;
  status: number;
}

export interface GroupDocumentEntity {
  id: number;
  group_id: number;
  title: string;
  status: number;
  document_id?: number;
  position?: number;
  paragraph?: ParagraphEntity;
}

export interface CharacterCard {
  id: number;
  voice_id: number;
  fullname: string;
  age: string;
  gender: string;
  accent: string;
}
export interface GroupAssignAccountList extends Entity {
  id: number;
  paragraph?: ParagraphEntity;
  document_id: number;
  group_id: number;
  status: number;
  title: string;
}

export interface GroupAssignMemberList extends Entity {
  id: number;
  member: MemberEntity;
  group_id: number;
  member_id: number;
  status: number;
  approve_process: number;
}

export interface AccountExercisesList extends Entity {
  id: number;
  paragraph: ParagraphEntity;
  account_id: number;
  group_id: number;
  paragraph_id: number;
  start_at: number;
  end_at: number;
  created_at: number;
  members: number;
  members_finish: number;
  account: ManagerList;
  score: number;
}

export interface GroupMemberExercisesList extends Entity {
  id: number;
  member_id: number;
  account_exercise_id: number;
  speak: number;
  listen: number;
  created_at: number;
  paragraph: ParagraphEntity;
  speak_high: number;
  speak_medium: number;
  speak_low: number;
  note: string;
  member: MemberEntity;
  lookup: number;
  account_exercise: AccountExercisesList;
  token: string;
  wrong_total: number;
  correct_total: number;
}

export interface GroupSpeakList extends Entity {
  id: number;
  member_id: number;
  sentence_id: number;
  paragraph_id: number;
  character_id: number;
  created_at: number;
  score: number;
  content: string;
  template: string;
  url?: string;
  comment?: string;
}

export interface ManagerList extends Entity {
  id: number;
  fullname: string;
  phone: number;
  created_at: number;
  role_id: number;
}

export type ReportsEntity = {
  id: number;
  item: string;
  object_id: number;
  day: number;
  status: number;
  created_at: number;
  amount: number;
  point: number;
  transaction_type: number;
};

export type TransactionMemberEntity = {
  id: number;
  member_id: number;
  token_id: number;
  quantity: number;
  account_id: number;
  type: number;
  status: number;
  created_at: number;
  action: number;
  member?: MemberEntity;
  Account?: AccountEntity;
};

export type MemberReportsEntity = {
  total_report: ReportsEntity;
  speak_report: ReportsEntity;
  listen_report: ReportsEntity;
};
