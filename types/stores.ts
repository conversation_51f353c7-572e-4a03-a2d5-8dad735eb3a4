import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { RecordProcessEnum } from '@/configs/RecordProcessEnum';
import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import { SentenceProcessEnum } from 'configs/SentenceProcessEnum';
import {
  CategoryEntity,
  Character,
  CourseEntity,
  DocumentEntity,
  KnowledgeEntity,
  ParagraphEntity,
  PronounceEntity,
  SentenceEntity,
  SentencePosEntity,
  SentenceScoreEntity,
  TransactionLearnInfo,
} from 'types/model';

export type CategoryStoreProps = {
  categories: Array<CategoryEntity>;
  setCategories: (categories: Array<CategoryEntity>) => void;
};

export type LearnStoreProps = {
  transactionInfo: TransactionLearnInfo | null;
  isFinishLearn: boolean;
  activeTab: LearnTypeEnum;
  exerciseToken?: string;
  currentCourseId: number;
  currentSentenceId: number;
  showWord: boolean;
  sentenceGroupsId: number;
  documents: DocumentEntity[];
  course: CourseEntity | null;
  activeDocument: DocumentEntity | null;
  activeParagraph: ParagraphEntity | undefined;
  paragraphs: ParagraphEntity[];
  conversations: Array<Array<SentenceEntity>>;
  speakings: Array<Array<SentenceEntity>>;
  conversationsByParagraph: Array<SentenceEntity[]>;
  setActiveDocument: (activeDocument: DocumentEntity) => void;
  setDocuments: (documents: DocumentEntity[]) => void;
  setActiveParagraph: (activeParagraph: ParagraphEntity | undefined) => void;
  setCourseId: (courseId: number) => void;
  setCourse: (course: CourseEntity) => void;
  setCurrentSentenceId: (sentenceId: number) => void;
  setConversations: (conversations: Array<SentenceEntity[]>) => void;
  setSpeakings: (conversations: Array<Array<SentenceEntity>>) => void;
  setConversationsByParagraph: (conversations: Array<SentenceEntity[]>) => void;
  setShowWord: (showWord: boolean) => void;
  activeCharacter?: Character | undefined | null;
  setCharacter: (character: Character | null) => void;
  selectedSentence: SentenceEntity | null;
  setSelectedSentence: (sentence: SentenceEntity | null) => void;
  setParagraphs: (paragraphs: ParagraphEntity[]) => void;
  setFinishLearn: (isFinishLearn: boolean) => void;
  setActiveTab: (tab: LearnTypeEnum) => void;
  setExerciseToken: (token: string) => void;
  setTransactionInfo: (transactionInfo: TransactionLearnInfo) => void;
  setSentenceGroupsId: (sentenceGroupsId: number) => void;
};
export type SpeakingStoreProps = {
  sentenceProcess: SentenceProcessEnum;
  setSentenceProcess: (sentenceProcess: SentenceProcessEnum) => void;
  recordProcess: RecordProcessEnum;
  setRecordProcess: (speakingProcess: RecordProcessEnum) => void;
  sentence: SentenceEntity | null;
  setSentence: (sentence: SentenceEntity | null) => void;
  volume: number;
  setVolume: (volume: number) => void;
  autoReading: boolean;
  setAutoReading: (autoReading: boolean) => void;
  replay: boolean;
  setReplay: (replay: boolean) => void;
  sentenceScore: SentenceScoreEntity | null;
  setSentenceScore: (sentenceScore: SentenceScoreEntity) => void;
  setTimestamp: (timestamp: number) => void;
  timestamp: number;
};
export type HeaderStoreProps = {
  title: string;
  setTitle: (title: string) => void;
};

export type LayoutStoreProps = {
  enableScroll: boolean;
  setEnableScroll: (enableScroll: boolean) => void;
};

export type SearchStoreProps = {
  keyword: string;
  keywordDebounce: string;
  type: string;
  pageSearch: number;
  setKeyword: (keyword: string) => void;
  setKeywordDebounce: (keywordDebounce: string) => void;
  setType: (type: string) => void;
  setPageSearch: (pageSearch: number) => void;
};

export type LoadingStoreProps = {
  loading: boolean;
  countRequest: number;
  increaseLoading: () => void;
  decreaseLoading: (initLoading: boolean) => void;
};

export type SentenceStoreProps = {
  currentCourseId: number | null;
  currentDocumentId: number | null;
  currentParagraphId: number | null;
  currentSentenceId: number | null;
  showWord: boolean;
  activeDocument: DocumentEntity | null;
  activeParagraph: ParagraphEntity | null;
  timeToLearnOneConversation: number;
  conversations: Array<Array<SentenceEntity>>;
  speakings: Array<Array<SentenceEntity>>;
  conversationsByParagraph: Array<SentenceEntity[]>;
  setActiveDocument: (activeDocument: DocumentEntity) => void;
  setActiveParagraph: (activeParagraph: ParagraphEntity | null) => void;
  setCourseId: (courseId: number) => void;
  setCurrentDocumentId: (documentId: number) => void;
  setCurrentParagraphId: (paragraphId: number) => void;
  setCurrentSentenceId: (sentenceId: number) => void;
  setConversations: (conversations: Array<SentenceEntity[]>) => void;
  setSpeakings: (conversations: Array<Array<SentenceEntity>>) => void;
  setConversationsByParagraph: (conversations: Array<SentenceEntity[]>) => void;
  setTimeToLearnOne: (time: number) => void;
  setShowWord: (showWord: boolean) => void;
  activeCharacter?: Character | null;
  setCharacter: (character: Character) => void;
  selectedSentence: SentenceEntity | null;
  setSelectedSentence: (sentence: SentenceEntity) => void;
};

export type WordStoreProps = {
  currentCourseId: number | null;
  currentDocumentId: number | null;
  currentParagraphId: number | null;
  currentSentenceId: number | null;
  showWord: boolean;
  activeDocument: DocumentEntity | null;
  activeParagraph: ParagraphEntity | null;
  timeToLearnOneConversation: number;
  conversations: Array<Array<SentenceEntity>>;
  speakings: Array<Array<SentenceEntity>>;
  conversationsByParagraph: Array<SentenceEntity[]>;
  activeSentencePos: SentencePosEntity | null;
  setActiveDocument: (activeDocument: DocumentEntity) => void;
  setActiveParagraph: (activeParagraph: ParagraphEntity | null) => void;
  setCourseId: (courseId: number) => void;
  setCurrentDocumentId: (documentId: number) => void;
  setCurrentParagraphId: (paragraphId: number) => void;
  setCurrentSentenceId: (sentenceId: number) => void;
  setConversations: (conversations: Array<SentenceEntity[]>) => void;
  setSpeakings: (conversations: Array<Array<SentenceEntity>>) => void;
  setConversationsByParagraph: (conversations: Array<SentenceEntity[]>) => void;
  setTimeToLearnOne: (time: number) => void;
  setShowWord: (showWord: boolean) => void;
  activeCharacter?: Character | null;
  setCharacter: (character: Character) => void;
  selectedSentence: SentenceEntity | null;
  setSelectedSentence: (sentence: SentenceEntity) => void;
  setSelectedSentencePos: (sentencePos: SentencePosEntity) => void;
};

export type PronounceStoreProps = {
  activePronounceId: number | null;
  currentCourseId: number | null;
  currentDocumentId: number | null;
  currentParagraphId: number | null;
  currentSentenceId: number | null;
  showWord: boolean;
  activeDocument: DocumentEntity | null;
  activeParagraph: ParagraphEntity | null;
  timeToLearnOneConversation: number;
  conversations: Array<Array<SentenceEntity>>;
  speakings: Array<Array<SentenceEntity>>;
  conversationsByParagraph: Array<SentenceEntity[]>;
  activeSentencePos: SentencePosEntity | null;
  activePronounce: PronounceEntity | null;
  setActivePronounceId: (activePronounceId: number) => void;
  setActiveDocument: (activeDocument: DocumentEntity) => void;
  setActiveParagraph: (activeParagraph: ParagraphEntity | null) => void;
  setCourseId: (courseId: number) => void;
  setCurrentDocumentId: (documentId: number) => void;
  setCurrentParagraphId: (paragraphId: number) => void;
  setCurrentSentenceId: (sentenceId: number) => void;
  setConversations: (conversations: Array<SentenceEntity[]>) => void;
  setSpeakings: (conversations: Array<Array<SentenceEntity>>) => void;
  setConversationsByParagraph: (conversations: Array<SentenceEntity[]>) => void;
  setTimeToLearnOne: (time: number) => void;
  setShowWord: (showWord: boolean) => void;
  selectedSentence: SentenceEntity | null;
  setSelectedSentence: (sentence: SentenceEntity) => void;
  setSelectedSentencePos: (sentencePos: SentencePosEntity) => void;
  setActivePronounce: (pronounce: PronounceEntity) => void;
};
export type KnowledgeStoreProps = {
  knowledge: KnowledgeEntity | null;
  setKnowledge: (knowledge: KnowledgeEntity) => void;
};
export type SidebarStoreProps = {
  isOpenSidebar: boolean;
  setOpenSidebar: (isOpen: boolean) => void;
};
export type OnboardingStoreProps = {
  questionId: number;
  setQuestionId: (questionId: number) => void;
  questionContent: string;
  setQuestionContent: (questionContent: string) => void;
  questionType: number;
  setQuestionType: (questionType: number) => void;
  answerReqs: Record<number, { id: number; content: string }>;
  setAnswerReqs: (
    updateFn: (
      prevAnswerReqs: Record<number, { id: number; content: string }>
    ) => Record<number, { id: number; content: string }>
  ) => void;
  questionStep: number;
  setQuestionStep: (questionStep: number) => void;
};

export type ReportStoreProps = {
  date: Date;
  setDate: (date: Date) => void;
  loading: boolean;
  params: {
    start_day: number;
    end_day: number;
    item: ReportTypeEnum | '';
    object_id: number;
    memberId?: number;
  };
  setLoading: (loading: boolean) => void;
  setParams: (params) => void;
};

export type BalanceStoreProps = {
  balance: number;
  balanceString: string;
  balanceStatus: number;
  setBalance: (balance: number) => void;
  setBalanceStatus: (balanceStatus: number) => void;
  setBalanceString: (balance: string) => void;
};
