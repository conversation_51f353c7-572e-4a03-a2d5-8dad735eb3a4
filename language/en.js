export default {
  message: {
    errorNotice: 'Notice',
    success: {
      common: 'Success',
      create_paragraph: 'Create paragraph successfully!',
      update: 'Update successfully!',
      create_gallery: 'Create gallery successfully!',
      delete_gallery: 'Delete gallery successfully!',
    },
    error: {
      nickname: {
        empty: 'Need Nickname',
        atleast: 'Nickname must be at least 4 characters',
      },
      fullname: 'Need Fullname',
      password: {
        invalid: 'Invalid password',
        empty: 'Need Password',
        atleast: 'Password must be 8 characters',
        case: 'Password must contain uppercase and lowercase letters',
        number: 'Password must contain numbers',
        special: 'Password must contain special characters',
        notMatch: "Password doesn't match",
      },
      pin: {
        empty: 'Need PIN',
        atleast: 'PIN must be 4 digits',
      },
      exit: 'Error occurred',
      manager: {
        accountManager: 'tài khoản này đã được phân công để quản lý tổ chức',
      },
      unknown: 'Something went wrong!',
      gallery: {
        empty_content: 'Please fill in the content or upload a new image!',
        empty_image: 'Please upload at least 1 image!',
        content: 'Please fill in all the content!',
      },
    },
    time_invalid: '<PERSON><PERSON><PERSON> tập này đã hết hạn làm. Hãy hỏi giáo viên để làm bài tập khác',
    toast_loading: 'In Progress ...',
  },
  menu: {
    account: {
      addMember: 'Add Member',
      config: 'Setting',
      logout: 'Log out',
    },
  },
  search: {
    no_search_result: 'No matching information found',
    popular_keyword: 'Keywords',
    search_history: 'Search History',
    placeholder: 'Search a course, learn ...',
    lesson: 'Lesson',
  },
  settings: {
    avatar: 'Avatar',
    upload: 'Upload',
    fullname: 'Fullname',
    nickname: 'Nickname',
    title: 'Settings',
    account: {
      title: 'Account',
      description: 'Manage personal information and security',
    },
    member: {
      title: 'Student',
      change_pin: 'Change password',
      description: 'Students in your account and login information',
      password: 'Password',
      rePassword: 'Confirm Password',
      add: {
        title: 'Add student',
        description: 'Add a student to your account',
        pin: 'Create PIN',
      },
      edit: {
        title: 'Change student information',
        description: 'Change student information in your account',
        button: 'Save',
        pinDescription: 'PIN is used to authenticate students',
        pin: 'Change PIN',
      },
    },
    workspace: {
      title: 'Workspace',
      description: 'Manage learning space appropriately',
      theme: {
        title: 'Theme',
        description: 'Choose your favorite color scheme',
      },
      fontsize: {
        title: 'Font size',
        description: 'Customize the font size',
      },
      language: {
        title: 'Language',
        description: 'Choose the language you want to display',
      },
      behavior: {
        title: 'Behavior',
        pointer: {
          title: 'Use mouse pointer',
          description: 'Replace the cursor with a hand when you move the mouse over a button',
        },
      },
    },
    transactions: {
      heading_page: 'Việc sử dụng',
      descriptionTransaction: 'Báo cáo sử dụng theo tháng, thành viên trong tài khoản',
      all_member: 'Tất cả thành viên',
      use_by_month: 'Sử dụng theo tháng',
      listen_by_month: 'Nghe theo tháng',
      read_by_month: 'Nói theo tháng',
      listen: 'Nghe',
      read: 'Nói',
    },
    usage: {
      heading_page: 'Lịch sử nạp',
      descriptionUsage: 'Lịch sử nạp kim cương',
    },
  },
  auth: {
    phone: 'Phone',
    fullname: 'Full name',
    password: 'Password',
    rePassword: 'Re-enter password',
    forgotPass: 'Forgot password?',
    btnLogin: 'Login',
    labelRegister: 'If you do not have an account you can register.',
    linkRegister: 'here',
    labelOtp: 'Enter OTP',
    descriptionOtp: 'If you do not receive OTP please wait {second} seconds',
    btnNext: 'Next',
    btnBack: 'Back',
    btnSendOtp: 'Send OTP',
    resendOtp: 'Resend OTP',
    sendingOtp: 'Sending...',
    signOut: 'Logout',
    save: 'Save',
    error: {
      wrongFullname: 'You have not entered your name',
      wrongPhone: 'Invalid phone number',
      wrongPassword: 'Invalid Password',
      otpWrong: 'Invalid OTP',
    },
    passWrong: 'Invalid Password',
    error_password_not_match: 'Invalid Password',
    tokenWrong: 'Invalid Token',
  },
  onboard: {
    hello: 'Hello',
    welcome: 'Welcome',
    step: 'Step',
    student: 'Students',
    welcome_note:
      'Besides you, you can create more members in your account, it can be your children or students etc.',
    name: 'Tên',
    password: 'Password',
    password_note: 'numbers only',
    btn_save: 'Save',
    skip: 'Skip',
    add_member: 'Add member',
    btn_start: 'Start',
  },
  course: {
    title: 'Course',
    btn_filter: 'Filter',
    add_course: 'Add course',
    create_course: 'Create course',
    course_name: "Course's name",
    course_description: 'Description',
    upload_image: 'Upload Image',
    cancel_create: 'Cancel',
    confirm_create: 'Save',
    exercise: 'Exercise',
    auto_listen: 'Play',
    no_course: 'No courses found',
    no_document: 'No lessons found',
    learnNow: 'Learn now',
    no_document_in_course: 'No lessons found in the course <content>{title}</content>',
  },
  learn: {
    send: 'Send',
    welcome_conversation: 'This is a conversation between {characters} on the topic',
    finishConversation: 'You have completed the lesson.',
    finishExercise: 'You have completed the exercise.',
    learnAgain: 'Re-study',
    learnNewDocument: 'Learn new lesson',
    approve_error: 'An error occurred, please try again later!',
    conversation: 'Conversation',
    speaking: 'Speaking Practice',
    doHomeWork: 'Do your homework',
    welcome_listening: 'In this section, you will play a character to start practicing speaking.',
    finish: 'Finish',
    continue: 'Continue',
    start: 'Start',
    no_conversation: 'No Conversation!',
    note_start_learn: 'Study time will start counting after you click start studying.',
    note_not_enough_token: 'Your balance is insufficient. Please top up!',
    not_english_speech: 'Please speak in English to continue.',
    warningStart: 'To learn this lesson, please learn from the first lesson.',
    btn_approve: 'Approve',
    btn_approve_waiting: 'Pending processing',
    choose_character: 'Select a character to start speaking',
    btnCheck: 'Check',
    countDownLabel: 'Prepare for speaking exercises',
    btnGoToCurrentParagraph: 'Đến bài được phép học',
    btnStart: 'Bắt đầu',
    approved: 'Đã duyệt',
    editParagraph: 'Sửa bài',
    createParagraph: 'Create paragraph',
    startExercise: 'Start exercise',
    no_exercise: 'No exercise!',
    no_selected_answers: 'You have not selected any answers!',
    correct: 'Correct!',
    wrong: 'Wrong!',
    approve_success: 'Đoạn văn đã được duyệt thành công.',
    approve_already_approve: 'Đoạn văn này đã được duyệt',
    doExerciseAgain: 'Do exercise again',
    doNewExercise: 'Do new exercise',
    no_balance: 'Your balance is insufficient. Please top up!',
    modal: {
      confirmExit: {
        heading: 'Xác nhận rời khỏi trang',
        content: 'Bạn có chắc chắn muốn rời khỏi trang này?',
        btnConfirm: 'Xác nhận',
        btnCancel: 'Hủy',
      },
      createParagraph: {
        title: 'Title',
        type: 'Select paragraph type',
        description: 'Description',
        error: {
          title: 'Please enter the title',
          type: 'Please select the type',
          description: 'Please enter the description',
        },
      },
    },
  },
  home: {
    title: 'Home',
    viewMoreResult: 'View more',
    label_basic_english: 'Get to know English',
    label_welcome: 'To understand you',
    label_title: 'Date of birth',
    label_description: 'To help us choose lessons that are appropriate for your age',
    btn_save: 'Send',
    btn_check: 'Check',
  },
  sidebar: {
    balance: 'Balance',
    current_language: 'Display language',
    theme_chooser: 'Theme',
    language: {
      english: 'Eng',
      vietnamese: 'Viet',
    },
    right: {
      lookup: 'Lookup',
      function: 'Function',
      translate: 'Translation',
      pronounce: 'pronunciation',
      phrase: 'Phrase',
      definition: 'Definition',
      example: 'Example',
      pos: 'Part of speech',
    },
  },
  history: {
    title: 'History',
    tab: {
      word: 'Word',
      sentence: 'Sentence',
      paragraph: 'Lesson',
    },
    word: {
      title: 'Word',
      type: 'Type',
      inSentence: 'In sentence',
    },
    sentence: {
      title: 'Sentence',
      inLesson: 'In lesson',
    },
    paragraph: {
      title: 'Lesson',
      category: 'Category',
    },
  },
  favourite: {
    title: 'Favourite',
  },
  conversation: {
    title: 'Conversation',
  },
  essay: {
    title: 'Esaay',
  },
  gallery: {
    title: 'Gallery',
    add_image_to_gallery: 'Add images to the gallery',
    edit_image: 'Edit sentence group',
    save: 'Save',
    cancel: 'Cancel',
    create_gallery: 'Create album',
    form: {
      add_image: 'Add image',
      or_drag_drop: 'Or drag and drop',
      edit_content: 'Edit content',
      edit_content_placeholder: 'Enter content ...',
    },
    delete: {
      title: 'Delete image',
      content: 'Are you sure you want to delete this image?',
    },
  },
  pronounce: {
    title: 'Pronounce',
  },
  knowledge: {
    title: 'Knowledge',
  },
  member: {
    titlePageLogin: 'Login',
  },
  group: {
    title: 'Group',
    common: {
      phone: 'Phone',
      manager: 'Manager',
    },
    tab: {
      dashboard: 'Dashboard',
      class: 'Class',
      manager: 'Manager',
      member: 'Member',
      assignment: 'Assignment',
      roadmap: 'Learning Roadmap',
      speak: 'Speak',
    },
    button: {
      addOrganization: 'Add Organization',
      addClass: 'Add Class',
      addManager: 'Add Manager',
      delete: 'Delete',
    },
    assignment: {
      title: 'Assignment',
      startTime: 'Start Time',
      endTime: 'End Time',
      create_title: 'Create Assignment',
      complete: 'Complete',
      quantityMember: 'Quantity Member',
      assignTo: 'Assign to',
      placeholderAssignmentMember: 'Choose member',
      placeholderAssignment: 'Choose exercise',
      placeholderTime: 'Choose date',
      assignAll: 'All member',
      placeholderNote: 'Note to member',
    },
    name: 'Name',
    address: 'Address',
    user_manager: 'User Manager',
    no_groups: 'No Groups',
    no_member_groups: 'No Member group',
    class: {
      request: 'Request',
      join_note:
        'To join the class, please send a request to join and wait for the class manager to accept',
      management: 'Class management',
      request_sent_false: 'Request sent error',
      request_being_processed: 'Your request is being processed',
      request_sent_sus: 'Request sent successfully',
      invalid_group: 'Invalid Group',
      copy_link: 'Copy link',
      add: 'Add Class',
      addMember: 'Add Member',
      assignAssignments: 'Assign assignments',
      name: 'Name',
      phone: 'Phone',
      no: 'Mã học viên',
      diamondBalance: 'Diamond balance',
      no_member: 'No members found',
      reject: 'Reject',
      accept: 'Accept',
      alreadyMember: 'Already A Member',
      rejected: 'Rejected',
      success: 'Success',
      reject_success: 'Reject success',
      accept_success: 'Accept success',
      assignedExercise: 'Bài giao',
      learnProgram: 'Chương trình học',
      addCharacter: 'Thêm nhân vật',
      remove: 'Bỏ khỏi lớp',
      view_member: 'Xem chi tiết thành viên',
      balance: 'Số sử dụng của lớp',
      linkMemberLogin: 'Sao chép link đăng nhập của thành viên',
    },
    start_time: 'Start time',
    end_time: 'End time',
    dashboard: {
      point: {
        green: 'Green point',
        orange: 'Orange point',
        red: 'Red point',
      },
      revenue_by_month: 'Diamond by month',
      topup_history: 'Diamond history',
      view_all: 'View all',
      topup: 'Topup',
      balance: 'Balance',
      label_chart_group_member_number: 'Number of students',
      label_chart_group_token: 'Doanh thu theo tháng',

      label_chart_class_point: '',
      label_chart_class_token: 'Use diamond by month',

      label_chart_member_point: 'Use by student',
      label_chart_member_token: 'Use by month',
    },
    exercise_detail: {
      header:
        "<span class='text-color-major'>{paragraphTitle}</span> dành cho <span class='text-color-major'>{fullname}</span>",
    },
  },
  calendar: {
    months: {
      th1: 'January',
      th2: 'February',
      th3: 'March',
      th4: 'April',
      th5: 'May',
      th6: 'June',
      th7: 'July',
      th8: 'August',
      th9: 'September',
      th10: 'October',
      th11: 'November',
      th12: 'December',
    },
  },
  paragraph: {
    edit: {
      btnSave: 'Lưu lại',
      label_title_character: 'Nhân vật',
      label_title_content: 'Nội dung',
      content_save_success:
        ' Lưu thành công.\n' + '        <br />\n' + '        Hội thoại của bạn đang được xử lý!',
      placeholder_input: 'Điền nội dung',
    },
  },
};
