import { QUERY_KEY } from '@/constant/query-key';
import { ParagraphEntity } from '@/types/model';
import axiosConfig from '@/utils/axios.config';
import { useQuery } from '@tanstack/react-query';
import { ApiEndpoints } from 'configs';
import { useSession } from '@/hooks/useSession';

export const useParagraph = ({ keyx }: { keyx: string }) => {
  const { data: session } = useSession();
  return useQuery({
    queryKey: [QUERY_KEY.PARAGRAPH, keyx],
    queryFn: async (): Promise<ParagraphEntity> => {
      try {
        const response = await axiosConfig.get(`${ApiEndpoints.PARAGRAPH_DETAIL}/${keyx}`, {
          headers: {
            'x-access-token': session?.accessToken,
          },
        });
        return response.data;
      } catch (error: any) {
        throw error;
      }
    },
    staleTime: Infinity,
    enabled: !!keyx,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
    retry: false,
  });
};
