import { useEffect } from 'react';

import { ApiEndpoints } from '@/configs';
import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import useReportStore from '@/store/report';
import { UseReportLearnProps } from '@/types/hooks';
import { ReportsEntity } from '@/types/model';
import useSWRImmutable from 'swr/immutable';

const useReportLearns = (params: UseReportLearnProps, shouldNotFetch: boolean) => {
  const { setLoading } = useReportStore();
  // @ts-ignore
  const queryParamsReportLearn = new URLSearchParams(params);
  const { data, isLoading } = useSWRImmutable(
    shouldNotFetch || params.type === '' || !Object.values(ReportTypeEnum).includes(params.type)
      ? null
      : `${ApiEndpoints.REPORT_LEARNS}?${queryParamsReportLearn}`
  );
  useEffect(() => {
    if (data) {
      setLoading(isLoading);
    }
  }, [isLoading]);

  return {
    reportsData: ((data?.data?.report_learns ?? null) as ReportsEntity[]) || null,
    isLoading,
  };
};
export default useReportLearns;
