export const ApiEndpoints = Object.freeze({
  BASE_URI: process.env.NEXT_PUBLIC_BASE_API,
  REQUEST_REGISTER: '/v1/api/account/register/request',
  SEND_OTP: '/v1/api/account/register/request',
  VERIFY_OTP: '/v1/api/account/register/verify',
  SAVE_PASSWORD: '/v1/api/account/register/make-account',
  LOGIN: '/v1/api/account/login',
  FORGOT_PASSWORD: '/v1/api/account/forgot-password',
  GET_USER_CONFIG: '/user-config',
  REFRESH_TOKEN: '/refresh-token',
  GET_QUESTION: '/v1/api/library/quiz/questions',
  GET_ANSWERS: '/v1/api/library/quiz/answers',
  SAVE_ANSWERS: '/v1/api/transaction/quiz/member-answer',
  CANCEL_ANSWER: '/v1/api/transaction/quiz/cancel-member-answer',
  GET_SAVE_ANSWERS: '/v1/api/transaction/quiz/member-answers',

  LOGIN_MEMBER: '/v1/api/account/members/login',
  LIST_MEMBER: '/v1/api/account/members',
  ADD_MEMBER: '/v1/api/account/members',
  CHANGE_PASS_MEMBER: '/v1/api/account/change-member-password',

  COURSE_LIST: '/v1/api/library/courses',
  COURSE_DETAIL: '/v1/api/course/course',

  DOCUMENT_LIST: '/v1/api/library/documents',
  DOCUMENT_DETAIL: '/v1/api/library/document',

  PARAGRAPH_LIST: '/v1/api/library/paragraphs',
  PARAGRAPH_DETAIL: '/v1/api/library/paragraphs',
  GROUP_PARAGRAPH_CHANGE_POSITION: '/v1/api/config/group-documents/update-position',

  END_PARAGRAPH: '/v1/api/transaction/paragraph',
  START_PARAGRAPH: '/v1/api/transaction/start-paragraph',

  SENTENCE_LIST_V1: '/v1/api/library/sentences',
  SENTENCE_LIST_V2: '/v2/api/library/sentences',
  SENTENCE_DETAIL: '/v1/api/course/document',
  SENTENCE_UPDATE: '/v1/api/library/update-sentences',
  SENTENCES_CHANGE_POSITION: 'v1/api/library/sentence/update-position',
  BACK_SENTENCE: '/v1/api/transaction/learn/back-sentence',

  CATEGORY_LIST: '/v1/api/account/categories',
  SEARCH_ENGINE_PY: '/v1/api/library/engine/search',
  SEARCH_ENGINE: '/v1/api/library/search',

  TRANSLATE_SENTENCE: '/v1/api/library/sentence-detail',
  DICTIONARY: '/v1/api/library/learn/listen/pos',

  END_LISTEN: '/v1/api/library/learn/listen/end',
  END_SPEAK: '/v1/api/library/learn/speak/end',

  APPROVED_PARAGRAPH: '/v1/api/library/approved-paragraph',

  HISTORY_SENTENCE: '/v1/api/transaction/sentences',
  HISTORY_PARAGRAPH: '/v1/api/transaction/paragraphs',
  GET_GROUPS: '/v1/api/config/groups',
  GET_GROUPS_BY_ID: '/v1/api/config/groups/get-group',
  GET_GROUPS_BY_TOKEN: '/v1/api/config/groups',
  ADD_GET_GROUPS_BY_TOKEN: '/v1/api/config/member-groups',
  GET_GROUPS_DOCUMENT: 'v1/api/config/group-documents',
  SAVE_GROUPS_DOCUMENT: 'v1/api/config/group-documents',
  APPROVE_MEMBER_GROUPS: '/v1/api/config/approve-member-groups',
  HISTORY_WORD: '/v1/api/transaction/lookups',
  FAVOURITE_LIST: '/v1/api/transaction/favourites',
  NEW_LIST: '/v1/api/transaction/news',

  GET_PRONOUNCE_ID: '/v1/api/library/pronounces',

  SAVE_CONFIG_MEMBER_CATEGORIES: '/v1/api/config/member-categories',
  GET_CONFIG_MEMBER_CATEGORIES: '/v1/api/config/member-categories',

  KNOWLEDGE_LIST: '/v1/api/library/knowledges',
  TAG_DETAILS_LIST: '/v1/api/library/tag-details',
  TAGS_LIST: '/v1/api/library/tags',

  UPDATE_ACCOUNT: '/v1/api/account/update-account',
  MARK_TRANSACTION_START_LEARN: '/v1/api/transaction/learn/start',
  MARK_TRANSACTION_END_LEARN: '/v1/api/transaction/learn/end',

  SENTENCE_GROUPS: '/v1/api/library/sentence-groups',

  GROUP_ACCOUNT_EXERCISES: '/v1/api/config/account-exercises',
  GROUP_ASSIGN_MEMBER: '/v1/api/config/member-exercises',
  GROUP_ASSIGN_DOCUMENTS: '/v1/api/config/group-documents',
  SPEAK_LIST: '/v1/api/library/learn/speak/list',
  CHARACTER_LIST: '/v1/api/library/characters',
  CHARACTER_UPSERT: 'v1/api/library/upsert-character',

  REPORT_POINTS: '/v1/api/transaction/report/report-points',
  REPORT_MEMBERS: '/v1/api/transaction/report/report-members',
  REPORT_TOKENS: '/v1/api/transaction/report/report-tokens',
  REPORT_MEMBER_TOKEN: '/v1/api/transaction/report-member-token/list',
  REPORT_LEARNS: '/v1/api/transaction/report/report-learns',

  MANAGER_LIST: '/v1/api/account/account-groups',
  ACCOUNT_BY_PHONE: '/v1/api/account/accounts',

  UPDATE_SENTENCE_GROUP_POSITION: 'v1/api/library/sentence-groups/update-position',
  TRANSACTION_RECOMMEND: '/v1/api/transaction/recommend',
  LIBRARY_ENGINE_LIST_PARAGRAPH: '/v1/api/library/engine/list-paragraph',
  UPDATE_TRANSACTION_SPEAK: '/v1/api/transaction/speak/update',
  EXERCISES: '/v1/api/library/quiz/questions',
  EXERCISE: 'v1/api/library/quiz/answers',
  ANSWERS: 'v1/api/transaction/quiz/member-answers',
  TRANSACTION_MEMBER_TOKEN: '/v1/api/transaction/member-token/list',
  TRANSACTION_ACCOUNT_TOKEN: '/v1/api/transaction/account-token/list',
  GET_BALANCE: '/v1/api/transaction/get-member-token',
  INVATE_MEMBERS_IN_GROUP: 'v1/api/config/member-groups/request-approve',
});
