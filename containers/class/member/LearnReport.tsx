'use client';

import React, { useMemo } from 'react';

import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import { TransactionTypeEnum } from '@/configs/TransactionTypeEnum';
// import { format } from 'date-fns';
import ChartBarSkeleton from '@/containers/class/skeleton/ChartBarSkeleton';
import useReportStore from '@/store/report';
import { ChartItem } from '@/types/component';
import { ReportsEntity } from '@/types/model';
import { map, reduce } from 'lodash';

import Charts from '@/components/Charts/Charts';

import useChartConfig from '@/hooks/Ent/useChartConfig';
import useReportLearns from '@/hooks/Ent/useReportLearns';

const LearnReport = ({ title }) => {
  const { makeBarDateToken } = useChartConfig();
  const { params, loading } = useReportStore();
  // console.log('params', params);
  const { reportsData, isLoading } = useReportLearns(
    {
      member_id: params.memberId,
      type: ReportTypeEnum.LEARN,
      start_at: params.start_day,
      end_at: params.end_day,
    },
    loading || params.start_day === 0 || params.end_day === 0
  );
  // console.log('reportsData', reportsData);
  const [charts, columns, totalBalance] = useMemo(() => {
    const colors = ['BLUE'];
    const charts: ChartItem[] = makeBarDateToken(reportsData, colors);
    // console.log('charts', charts);
    //tổng hợp theo ngày
    const groupedByDay = reduce(
      reportsData,
      (acc, item) => {
        if (!acc[item.day]) {
          acc[item.day] = [];
        }
        acc[item.day].push(item);
        return acc;
      },
      {}
    );
    map(groupedByDay, (items: ReportsEntity[], key) => {
      //tổng hợp điểm theo ngày
      const groupedPoint = reduce(
        items,
        (acc, item) => {
          if (!acc[item.transaction_type]) {
            acc[item.transaction_type] = 0;
          }
          acc[item.transaction_type] += item.amount;
          return acc;
        },
        {}
      );
      const index = charts.findIndex((item) => item.date === parseInt(key));
      // console.log('index', index);
      if (index >= 0) {
        // charts[index] = {
        //   day: format(parseInt(key) * 86400 * 1000, 'dd'),
        //   date: parseInt(key),
        //   BLUE: groupedPoint[TransactionTypeEnum.TRANSACTION_EARN],
        //   GREEN: groupedPoint[TransactionTypeEnum.TRANSACTION_BURN],
        //   isActive: true,
        // };
        charts[index - 1] = {
          day: key,
          date: parseInt(key),
          BLUE: groupedPoint[TransactionTypeEnum.TRANSACTION_EARN],
          GREEN: groupedPoint[TransactionTypeEnum.TRANSACTION_BURN],
          isActive: true,
        };
      }
    });

    const totalBalance = reduce(
      reportsData,
      (totalBalance, item) =>
        item.transaction_type === TransactionTypeEnum.TRANSACTION_BURN
          ? totalBalance + item.amount
          : totalBalance,
      0
    );
    return [charts, colors, totalBalance];
  }, [reportsData]);
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || payload.length === 0 || !payload.some((p) => p.value > 0)) {
      return null;
    }
    // const total = payload.reduce((current, item) => item.value + current, 0);

    const genTransactionName = (id) => {
      const ids = {};
      return ids[id] ?? '#';
    };

    return (
      <div className="bg-bg-box p-3 rounded-md shadow-md">
        <p className="font-bold">Ngày {label}</p>
        {payload.reverse().map((entry, index) => (
          <p
            key={index}
            className="text-xs py-1 flex items-center gap-2"
            style={{ color: entry.color }}
          >
            <i className={'w-3 h-3 inline-block'} style={{ backgroundColor: entry.color }}></i>
            {genTransactionName(entry.name)}: {entry.value} Kim cương
          </p>
        ))}
      </div>
    );
  };

  return (
    <>
      <div className={'col-span-12 sm:col-span-4 md:col-span-3 2xl:col-span-2'}>
        <div className={'grid grid-cols-3 mb-[30px]'}>
          <div className={'col-span-3 flex'}>
            <div className="mr-[30px] px-[6px]">
              <i className="text-[32px] icon-diamond text-yellow-100" />
            </div>
            <div>
              <div className={'text-small'}>Số ngày học liên tục</div>
              <span className={'text-[22px] font-medium'}>{totalBalance}</span>
            </div>
          </div>
        </div>
        <div className={'grid grid-cols-3 mb-[30px]'}>
          <div className={'col-span-3 flex'}>
            <div className="mr-[30px] px-[6px]">
              <i className="text-[32px] icon-diamond text-yellow-100" />
            </div>
            <div>
              <div className={'text-small'}>Thời lượng học tuần</div>
              <span className={'text-[22px] font-medium'}>0</span>
            </div>
          </div>
        </div>
        <div className={'grid grid-cols-3 mb-[30px]'}>
          <div className={'col-span-3 flex'}>
            <div className="mr-[30px] px-[6px]">
              <i className="text-[32px] icon-diamond text-yellow-100" />
            </div>
            <div>
              <div className={'text-small'}>Bài học trong tuần</div>
              <span className={'text-[22px] font-medium'}>{totalBalance}</span>
            </div>
          </div>
        </div>
      </div>

      <div className={'col-span-12 sm:col-span-8 md:col-span-9 2xl:col-span-10'}>
        <div className={'w-full flex justify-between items-center'}>
          <span className={'text-small font-medium text-color-major'}>{title}</span>
        </div>
        <div className={'mt-2'}>
          <div className={'w-full relative h-[350px]'}>
            {isLoading ? (
              <ChartBarSkeleton />
            ) : (
              /*@ts-ignore*/
              <Charts charts={charts} columns={columns} tooltip={CustomTooltip} label={''} />
            )}
          </div>
        </div>
      </div>
    </>
  );
};
export default LearnReport;
