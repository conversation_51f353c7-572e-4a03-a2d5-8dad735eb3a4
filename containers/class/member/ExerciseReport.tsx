'use client';

import React, { useEffect, useMemo } from 'react';

import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
// import { TransactionTypeEnum } from '@/configs/TransactionTypeEnum';
// import { format } from 'date-fns';
import ChartBarSkeleton from '@/containers/class/skeleton/ChartBarSkeleton';
import useReportStore from '@/store/report';
import { ChartItem } from '@/types/component';
import { ReportsEntity } from '@/types/model';
import { endOfMonth, startOfMonth } from 'date-fns';
import { map, reduce } from 'lodash';

// import { useTranslations } from 'next-intl';

import Charts from '@/components/Charts/Charts';

import useChartConfig from '@/hooks/Ent/useChartConfig';
import useReportLearns from '@/hooks/Ent/useReportLearns';

// import useReportPoints from '@/hooks/Ent/useReportPoints';

const ExerciseReport = ({ title }) => {
  // const t = useTranslations();
  const { params, setParams, date, loading } = useReportStore();
  const { makeBarDatePoint } = useChartConfig();

  const { reportsData, isLoading } = useReportLearns(
    {
      member_id: params.memberId,
      type: ReportTypeEnum.EXERCISE,
      start_at: params.start_day,
      end_at: params.end_day,
    },
    loading || params.start_day === 0 || params.end_day === 0
  );

  useEffect(() => {
    const startMonthDate = startOfMonth(date);
    const endMonthDate = endOfMonth(date);

    const start_day = Math.floor(
      Date.UTC(startMonthDate.getFullYear(), startMonthDate.getMonth(), startMonthDate.getDate()) /
        1000
    );
    const end_day = Math.floor(
      Date.UTC(endMonthDate.getFullYear(), endMonthDate.getMonth(), endMonthDate.getDate()) / 1000
    );
    setParams({
      ...params,
      start_day: start_day,
      end_day: end_day,
    });
  }, [date]);

  const [charts, columns] = useMemo(() => {
    const colors = ['BLUE', 'YELLOW', 'RED'];
    //tổng hợp theo ngày
    const groupedByDay = reduce(
      reportsData,
      (acc, item) => {
        if (!acc[item.day]) {
          acc[item.day] = [];
        }
        acc[item.day].push(item);
        return acc;
      },
      {}
    );
    const charts: ChartItem[] = makeBarDatePoint(reportsData, colors);
    map(groupedByDay, (items: ReportsEntity[], key) => {
      //tổng hợp điểm theo ngày
      const groupedPoint = reduce(
        items,
        (acc, item) => {
          if (!acc[item.point]) {
            acc[item.point] = 0;
          }
          acc[item.point] += item.amount;
          return acc;
        },
        {}
      );
      const index = charts.findIndex((item) => item.date === parseInt(key));
      if (index >= 0) {
        // charts[index] = {
        //   day: format(parseInt(key) * 86400 * 1000, 'dd'),
        //   date: parseInt(key),
        //   BLUE: groupedPoint[1],
        //   YELLOW: groupedPoint[2],
        //   RED: groupedPoint[3],
        //   isActive: true,
        // };
        charts[index - 1] = {
          day: key,
          date: parseInt(key),
          BLUE: groupedPoint[1],
          YELLOW: groupedPoint[2],
          RED: groupedPoint[3],
          isActive: true,
        };
      }
    });

    const sortedCharts = charts.sort((a, b) => Number(a.date) - Number(b.date));
    return [sortedCharts, colors];
  }, [reportsData]);

  // const sumTotalByColor = (color: string) => {
  //   const learnDate = charts?.filter((item) => item[color] > 0);
  //   const total = charts?.reduce((acc: number, item) => {
  //     acc += item[color];
  //     return acc;
  //   }, 0);
  //   // if (learnDate.length > 0) return (total / learnDate.length).toFixed(2);
  //   if (learnDate.length > 0) return total;
  //   return 0;
  // };

  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || payload.length === 0 || !payload.some((p) => p.value > 0)) {
      return null;
    }

    const total = payload.reduce((current, item) => item.value + current, 0);
    const mapColorName = (id) => {
      const ids = {
        BLUE: 'Xanh',
        YELLOW: 'Cam',
        RED: 'Đỏ',
      };
      return ids[id] ?? 'Đỏ';
    };

    return (
      <div className="bg-bg-box p-3 rounded-md shadow-md">
        <p className="font-bold">
          Ngày {label}: {total} point{' '}
        </p>
        {payload.reverse().map((entry, index) => (
          <p
            key={index}
            className="text-xs py-1 flex items-center gap-2"
            style={{ color: entry.color }}
          >
            <i className={'w-3 h-3 inline-block'} style={{ backgroundColor: entry.color }}></i>
            Số điểm {mapColorName(entry.name)}: {entry.value}
          </p>
        ))}
      </div>
    );
  };

  return (
    <>
      {/* <div className={'col-span-12 sm:col-span-4 md:col-span-3 2xl:col-span-2'}>
        <div className={'grid grid-cols-3'}>
          <div className={'col-span-3 flex'}>
            <div className="mr-[30px] px-[6px]">
              <i className="text-[32px] icon-diamond text-yellow-100" />
            </div>
            <div>
              <div className={'text-small'}>{label}</div>
              <span className={'text-[22px] font-medium'}>{totalBalance}</span>
              
            </div>
          </div>
        </div>
      </div> */}

      <div className={'col-span-12 sm:col-span-8 md:col-span-9 2xl:col-span-10'}>
        <div className={'w-full flex justify-between items-center'}>
          <span className={'text-small font-medium text-color-major'}>{title}</span>
        </div>
        <div className={'mt-2'}>
          <div className={'w-full relative h-[250px]'}>
            {isLoading ? (
              <ChartBarSkeleton />
            ) : (
              /*@ts-ignore*/
              <Charts charts={charts} columns={columns} tooltip={CustomTooltip} label={''} />
            )}
          </div>
        </div>
      </div>
    </>
  );
};
export default ExerciseReport;
