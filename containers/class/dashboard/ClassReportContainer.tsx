'use client';

import React, { useEffect } from 'react';

import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import ClassWithMemberReport from '@/containers/group/report-item/ClassWithMemberReport';
import PointsReport from '@/containers/group/report-item/PointsReport';
import TokenReport from '@/containers/group/report-item/TokenReport';
// import TotalMemberReport from '@/containers/class/dashboard/TotalMemberReport';
import useReportStore from '@/store/report';
import ScrollArea from 'components/ScrollArea';
import { useTranslations } from 'next-intl';

const ClassReportContainer = ({ group }) => {
  const t = useTranslations();
  const { params, setParams } = useReportStore();
  useEffect(() => {
    setParams({
      ...params,
      item: ReportTypeEnum.CLASS,
      object_id: group.id,
    });
  }, []);
  return (
    <ScrollArea
      className={'!h-[calc(100vh_-_43px)] relative w-full flex-1 bg-bg-general overflow-x-hidden'}
    >
      <div className="pt-[15px] pb-[30px] mb-20">
        <div className={'grid grid-cols-12 gap-4 px-[30px]'}>
          {/* <TotalMemberReport title={t('group.dashboard.label_chart_class_token')} /> */}
          <TokenReport
            title={t('group.dashboard.label_chart_group_token')}
            label={t('group.dashboard.balance')}
          />
        </div>

        <div className={'grid grid-cols-12 gap-4 mt-10 px-[30px]'}>
          <PointsReport />
        </div>

        <div className={'grid grid-cols-12 gap-4 mt-10 px-[30px]'}>
          <ClassWithMemberReport
            title={t('group.dashboard.label_chart_group_member_number')}
            item={ReportTypeEnum.CLASS}
          />
        </div>
      </div>
    </ScrollArea>
  );
};
export default ClassReportContainer;
