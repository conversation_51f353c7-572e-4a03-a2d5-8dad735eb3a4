'use client';

import React from 'react';

import Link from 'next/link';
import { useParams } from 'next/navigation';

import DocumentListSkeleton from '@/containers/document/skeleton/DocumentListSkeleton';
import Button from 'components/Button';
import FavouriteButton from 'components/FavouriteButton';
import GemIcon from 'components/Icons/GemIcon';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import useFavouriteAll from 'hooks/Ent/useFavouriteAll';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';
import useHeaderStore from 'store/header';

import useDocumentsList from '@/hooks/Ent/useDocumentsList';

const DocumentContainer = () => {
  const { favouriteAll } = useFavouriteAll();
  const params = useParams();
  const { title } = useHeaderStore();
  const course_id = params.id?.toString() || '';
  const { documentList, isLoading, isReachingEnd, page, setPage } = useDocumentsList({
    course_id: course_id ? parseInt(course_id) : undefined,
    title: params.keyword?.toString() || '',
  });
  const t = useTranslations();

  if (!isLoading && !documentList.length)
    return (
      <table className="table-auto w-full">
        <tbody>
          <tr>
            <td colSpan={3}>
              <div className={'flex h-72 justify-center w-full items-center flex-col'}>
                <i className={'text-5xl icon-alert-line animate-bounce opacity-50'} />
                {course_id && course_id !== '' ? (
                  <span
                    className={'text-color-minor'}
                    dangerouslySetInnerHTML={{
                      // @ts-ignore
                      __html: t.rich('course.no_document_in_course', {
                        content: (chuck) => `<span class='text-color-major'>${chuck}</span>`,
                        title: title ?? '',
                      }),
                    }}
                  />
                ) : (
                  <span
                    className={'text-color-minor'}
                    dangerouslySetInnerHTML={{ __html: t('course.no_document') }}
                  />
                )}
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    );
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        dataLength={documentList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <div
          className={'border-b border-bg-box h-0 w-full'}
          style={{
            borderBottomWidth: '10px',
          }}
        ></div>
        <table className="table-auto w-full">
          <tbody className={'text-[0.8123rem]'}>
            {documentList &&
              map(documentList, (doc, key) => (
                <tr
                  key={`tr-${key}`}
                  className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group [&>td>button]:hidden [&>td>button]:hover:flex"
                >
                  <td key={`td-1-${key}`} className={'p-2 pl-[30px] h-[42px] flex items-center'}>
                    <i className={'icon-document text-[16px] mr-2'} />
                    <Link key={`a-${key}`} href={`${EntRouters.document}/${doc.id}`}>
                      {doc.title}
                    </Link>
                    <FavouriteButton
                      favouriteList={favouriteAll}
                      item="document"
                      object_id={doc.id}
                      className="ml-2"
                    />
                  </td>
                  <td key={`td-2-${key}`} className={'p-2 text-purple w-[100px] hidden'}>
                    {doc.status === 2 ? (
                      <span className={'bg-bg-box px-2 py-1 rounded-sm'}>{t('course.payed')}</span>
                    ) : (
                      <span className={'flex items-center'}>
                        <GemIcon className={'fill-purple w-4 h-4 mr-1'} />
                        {doc.id}
                      </span>
                    )}
                  </td>
                  <td key={`td-3-${key}`} className={'w-[120px] pr-3'}>
                    {doc.status === 2 ? (
                      <Button
                        className={'group-hover:!flex hidden'}
                        as={'a'}
                        href={`${EntRouters.document}/${doc.id}`}
                        color={'primary'}
                        size={'xs'}
                      >
                        {t('course.learnNow')}
                      </Button>
                    ) : null}
                  </td>
                </tr>
              ))}
            {isLoading && <DocumentListSkeleton />}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default DocumentContainer;
