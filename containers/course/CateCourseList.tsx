'use client';

import React from 'react';

import Link from 'next/link';

import Button from 'components/Button';
import FavouriteButton from 'components/FavouriteButton';
import CategoryEnum from 'configs/CategoryEnum';
import useCateGetByParentId from 'hooks/Ent/useCateGetByParentId';
import useFavouriteAll from 'hooks/Ent/useFavouriteAll';
import { useMounted } from 'hooks/common/useMounted';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import { CategoryChildProps } from 'types/component';

const CategoryChild = ({ categories, position }: CategoryChildProps) => {
  const { favouriteAll } = useFavouriteAll();
  const t = useTranslations();
  const pl = position * 30;
  return (
    <>
      {categories &&
        categories.length > 0 &&
        categories.map((category) => (
          <React.Fragment key={`tr-${category.id}`}>
            <tr className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group [&>td>button]:hidden [&>td>button]:hover:flex">
              {category.title ? (
                <>
                  <td
                    className={'p-2 h-[42px] flex items-center'}
                    style={{ paddingLeft: `${pl}px` }}
                  >
                    <Link className={'flex'} href={`${category.keyx}/${category.valuex ?? ''}`}>
                      <i className={'icon-course text-[16px] mr-2'} />
                      {category.title}
                    </Link>
                    <FavouriteButton
                      favouriteList={favouriteAll}
                      item={'course'}
                      object_id={category.id}
                      className="ml-2"
                    />
                  </td>
                  <td className={'w-[120px] pr-3'}>
                    <Button
                      as={'a'}
                      href={`${category.keyx}/${category.valuex ?? ''}`}
                      color={'primary'}
                      size={'xs'}
                      className={'group-hover:!flex hidden'}
                    >
                      {t('course.learnNow')}
                    </Button>
                  </td>
                </>
              ) : (
                <td
                  key={`td-${category.id}`}
                  colSpan={2}
                  className={'bg-bg-box'}
                  style={{ lineHeight: '10px', height: '10px' }}
                >
                  {/*<div className={'border-b border-bg-box h-0 w-full'} style={{
                              borderBottomWidth: '10px'
                          }}></div>*/}
                </td>
              )}
            </tr>
            <CategoryChild key={category.id} categories={category.items} position={position + 1} />
          </React.Fragment>
        ))}
    </>
  );
};

const CateCourseList = () => {
  const t = useTranslations();
  const parent_id = CategoryEnum.COURSE_PARENT_ID;
  const isMounted = useMounted();
  const { categories } = useCateGetByParentId(parent_id.toString(), !isMounted, 'favourite');
  if (!isMounted) return <></>;
  if (!categories.length)
    return (
      <table className="table-auto w-full">
        <thead>
          <tr className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
            <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>{t('headerName')}</th>
            <th>&nbsp;</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colSpan={2}>
              <div className={'flex h-56 justify-center w-full items-center flex-col'}>
                <i className={'w-20 h-20 text-5xl  icon-alert-line animate-bounce opacity-50'} />
                <span dangerouslySetInnerHTML={{ __html: t('course.no_course') }} />
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    );

  return (
    <table className="table-auto w-full">
      <tbody className={'text-[0.8123rem]'}>
        {map(categories, (category) => (
          <React.Fragment key={`tr-${category.id}`}>
            <tr
              key={`tr-${category.id}`}
              className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}
            >
              <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>{category.title}</th>
              <th>&nbsp;</th>
            </tr>
            <CategoryChild key={category.id} categories={category.items} position={1} />
          </React.Fragment>
        ))}
      </tbody>
    </table>
  );
};
export default CateCourseList;
