'use client';

import { useState } from 'react';

import Link from 'next/link';
import { useParams } from 'next/navigation';

import CourseListSkeleton from '@/containers/course/skeleton/CourseListSkeleton';
import classNames from 'classnames';
import Button from 'components/Button';
import FavouriteButton from 'components/FavouriteButton';
import GemIcon from 'components/Icons/GemIcon';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import useCourse from 'hooks/Ent/useCourse';
import useFavouriteAll from 'hooks/Ent/useFavouriteAll';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';

const CourseListContainer = () => {
  const { favouriteAll } = useFavouriteAll();
  const params = useParams();
  const { courseList, isLoading, isReachingEnd, page, setPage } = useCourse({
    title: params.keyword?.toString() || '',
  });

  const t = useTranslations();
  const [isEdit, setEdit] = useState(false);
  const handleClickEdit = () => {
    setEdit(!isEdit);
  };

  if (!isLoading && !courseList.length)
    return (
      <table className="table-auto w-full">
        <thead>
          <tr className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
            <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>{t('headerName')}</th>
            <th className={'px-2 py-1 text-left font-normal hidden'}>{t('headerFee')}</th>
            <th>&nbsp;</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colSpan={3}>
              <div className={'flex h-56 justify-center w-full items-center flex-col'}>
                <i className={'w-20 h-20 text-5xl icon-course animate-bounce opacity-50'} />
                <span dangerouslySetInnerHTML={{ __html: t('course.no_course') }} />
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    );

  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        dataLength={courseList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead className="sticky top-0 z-[1]">
            <tr className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
              <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>{t('headerName')}</th>
              <th className={'px-2 py-1 text-left font-normal hidden'}>{t('headerFee')}</th>
              <th>&nbsp;</th>
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {map(courseList, (item, key) => (
              <tr
                key={`tr-${key}`}
                className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box [&>td>button]:hidden [&>td>button]:hover:flex"
              >
                <td key={`td-1-${key}`} className={'p-2 pl-[30px] h-[42px] flex items-center'}>
                  <Link key={`a-${key}`} href={`${EntRouters.course}/${item.id}`}>
                    {item.title}
                  </Link>
                  {/* <div onClick={handleClickFavorite}
                      key={`div-${key}`}
                      className={'flex items-center justify-center w-[28px] h-[27px] rounded-md ml-2 hover:bg-bg-box cursor-pointer'}>
                    <StarIcon
                      key={`icon-star-${key}`}
                      className={classNames('w-[15px] h-[16px] fill-color-minor', { '!fill-yellow': isFavorite })} />
                  </div> */}
                  <FavouriteButton
                    favouriteList={favouriteAll}
                    item={EntRouters.course}
                    object_id={item.id}
                    className="ml-2"
                  />
                  <div
                    onClick={handleClickEdit}
                    className={
                      'flex items-center justify-center w-[28px] h-[27px] rounded-md ml-2 hover:bg-bg-box cursor-pointer'
                    }
                  >
                    <i
                      key={`pen-star-${key}`}
                      className={classNames('w-[12px] h-[13px] icon-pencil-line fill-color-minor', {
                        '!fill-yellow': isEdit,
                      })}
                    />
                  </div>
                </td>
                <td key={`td-2-${key}`} className={'p-2 text-purple w-[100px] hidden'}>
                  {item.status === 2 ? (
                    <span className={'bg-bg-box px-2 py-1 rounded-sm'}>{t('payed')}</span>
                  ) : (
                    <span className={'flex items-center'}>
                      <GemIcon className={'fill-purple w-4 h-4 mr-1'} />
                      {item.status}
                    </span>
                  )}
                </td>
                <td key={`td-3-${key}`} className={'w-[120px]'}>
                  {item.status === 2 ? (
                    <Button color={'primary'} size={'xs'}>
                      {t('course.learnNow')}
                    </Button>
                  ) : null}
                </td>
              </tr>
            ))}
            {isLoading ? <CourseListSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default CourseListContainer;
