'use client';

import { useEffect, useMemo } from 'react';

import Image from 'next/image';
import { useRouter } from 'next/navigation';

import { ConversationTypeEnum } from '@/configs/ConversationEnum';
import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { STATUS_IMAGE_URL } from '@/constant';
import { useConversationContext } from '@/providers/ConversationProvider';
import { EntRouters, SPECIAL_DOCUMENT_ID } from 'configs';
import { useTranslations } from 'next-intl';
import useLearnStore from 'store/learn';

const FinishScreen = ({
  handleRestart,
  isExercise = false,
  handleDoNewExercise
}: {
  handleRestart: () => void;
  handleDoNewExercise: () => void;
  isExercise: boolean;
}) => {
  const t = useTranslations();
  const router = useRouter();
  const { exerciseToken } = useLearnStore();
  const { paragraph, paragraphs, activeTab, setActiveTab } = useConversationContext();

  useEffect(() => {
    const keyDownHandler = (event: KeyboardEvent) => {
      if (event.metaKey && event.key === 'Enter') {
        if (paragraph?.document_id === SPECIAL_DOCUMENT_ID) return;
        handleLearnNextParagraph();
      }
      if (event.shiftKey && event.key === 'Enter') {
        handleRestart();
      }
      if (event.ctrlKey && event.key === 'Enter') {
        if (activeTab === LearnTypeEnum.EXERCISE) {
          handleDoNewExercise?.();
        } else {
          if (paragraph?.item === ConversationTypeEnum.GALLERY) {
            setActiveTab(LearnTypeEnum.EXERCISE);
          } else {
            handleChangeTab(activeTab);
          }
        }
      }
    };

    window.addEventListener('keydown', keyDownHandler);
    return () => {
      window.removeEventListener('keydown', keyDownHandler);
    };
  }, [paragraph, activeTab, handleDoNewExercise]);

  const handleLearnNextParagraph = () => {
    const currentIndex = paragraphs?.findIndex((item) => item.id === paragraph?.id);
    if (currentIndex !== -1 && paragraphs && paragraphs[currentIndex + 1]) {
      const nextParagraph = paragraphs[currentIndex + 1];
      router.replace(`${EntRouters.learn}/${nextParagraph.keyx}`);
    }
  };

  const handleChangeTab = (tab: string) => {
    setActiveTab(tab === LearnTypeEnum.LISTEN ? LearnTypeEnum.SPEAKING : LearnTypeEnum.LISTEN);
  };

  const isGalleryOrExercise =
    paragraph?.item === ConversationTypeEnum.GALLERY || activeTab === LearnTypeEnum.EXERCISE;
  const isSpecialDocument = paragraph?.document_id === SPECIAL_DOCUMENT_ID;

  const statusImageUrl = useMemo(() => {
    return STATUS_IMAGE_URL[Math.random() > 0.5 ? 'NA.FINISH' : 'XO.FINISH'];
  }, []);

  return (
    <div className="text-center flex w-full flex-1 justify-center flex-col items-center" key="toolbox-conv">
      <h5 className="text-color-major text-[0.9375rem] text-center font-[SVN-Poppins-Regular]">
        {isExercise ? t('learn.finishExercise') : t('learn.finishConversation')}
      </h5>

      <Image
        src={statusImageUrl}
        width={128}
        height={128}
        className="mx-auto mt-8"
        alt="finish-image"
        objectFit="contain"
      />

      <div className="flex items-center justify-between mt-6 gap-x-5">
        {/* Learn Again Button */}
        <div>
          <div
            onClick={() => handleRestart()}
            className="w-32 cursor-pointer rounded-[5px] py-0.5 text-[0.75rem] border border-color-border bg-bg-box shadow-small flex items-center px-6"
          >
            <i className="text-base icon-arrow-left text-color-minor" />
            <span className="ml-2 mt-0.5">
              {activeTab === LearnTypeEnum.EXERCISE
                ? t('learn.doExerciseAgain')
                : t('learn.learnAgain')}
            </span>
          </div>
          <span className="text-color-minor text-[0.625rem] mt-2">Shift + Enter</span>
        </div>

        {/* Change Mode Button */}
        {!isGalleryOrExercise && (
          <div>
            <div
              onClick={() => handleChangeTab(activeTab)}
              className="w-32 cursor-pointer whitespace-nowrap rounded-[5px] py-0.5 text-[0.75rem] border border-color-border bg-bg-box shadow-small mr-2.5 flex items-center justify-center"
            >
              {activeTab === LearnTypeEnum.LISTEN ? (
                <>
                  <i className="text-base text-color-minor icon-mic" />
                  <span className="ml-2 mt-0.5">{t('learn.speaking')}</span>
                </>
              ) : (
                <>
                  <i className="text-base text-color-minor icon-conversation" />
                  <span className="ml-2 mt-0.5">{t('learn.conversation')}</span>
                </>
              )}
            </div>
            <span className="text-color-minor text-[0.625rem] mt-2">Control + Enter</span>
          </div>
        )}

        {paragraph?.item === ConversationTypeEnum.GALLERY && (
          <div>
            <div
              onClick={() => {
                setActiveTab(LearnTypeEnum.EXERCISE);
              }}
              className="w-32 cursor-pointer flex items-center justify-center rounded-[5px] py-0.5 text-[0.75rem] border border-color-border bg-bg-box shadow-small"
            >
              <span className="mr-2 mt-0.5">{t('learn.doHomeWork')}</span>
            </div>
            <span className="text-color-minor text-[0.625rem] mt-2">Control + Enter</span>
          </div>
        )}

        {/* Next Paragraph Button */}
        {!isSpecialDocument && exerciseToken === '' && (
          <div>
            <div
              onClick={() => {
                if (activeTab === LearnTypeEnum.EXERCISE) {
                  handleDoNewExercise?.();
                } else {
                  handleLearnNextParagraph();
                }
              }}
              className="w-32 cursor-pointer rounded-[5px] py-0.5 text-[0.75rem] border border-color-border bg-bg-box shadow-small flex items-center justify-end flex-wrap"
            >
              <span className="mr-2 mt-0.5">
                {activeTab === LearnTypeEnum.EXERCISE
                  ? t('learn.doNewExercise')
                  : t('learn.learnNewDocument')}
              </span>
              <i className="text-base icon-arrow-right-line text-color-minor" />
            </div>
            <span className="text-color-minor text-[0.625rem] mt-2">Command + Enter</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default FinishScreen;
