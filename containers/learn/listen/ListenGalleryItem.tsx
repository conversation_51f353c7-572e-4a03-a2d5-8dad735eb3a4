'use client';

import React from 'react';

import { SentenceEntity } from '@/types/model';
import classNames from 'classnames';
import { map } from 'lodash';
import useLearnStore from '@/store/learn';

export const ListenGalleryItem = ({
  sentences,
  className,
}: {
  sentences: SentenceEntity[];
  className?: string;
}) => {
  const { showWord } = useLearnStore();
  return (
    <div
      className={classNames(
        'space-y-1 text-xl items-center font-semibold flex flex-col w-full',
        {}
      )}
      style={{ textShadow: ' #000000 0px 0px 7px' }}
    >
      {sentences &&
        map(sentences, (sentence, index) => (
          <React.Fragment key={index}>
            <span
              className={classNames(
                className,
                'rounded-sm pr-1 first:-ml-0 cursor-pointer col-span-6 relative transition-all ease-in-out duration-1 font-svn text-white',
                {
                  'opacity-100': showWord,
                  'opacity-50': !showWord,
                }
              )}
              dangerouslySetInnerHTML={{ __html: sentence.content || '' }}
            />
          </React.Fragment>
        ))}
    </div>
  );
};
