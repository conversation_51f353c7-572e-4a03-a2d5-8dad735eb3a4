import { useConversationContext } from '@/providers/ConversationProvider';
import classNames from 'classnames';
import { ConversationTypeEnum } from 'configs/ConversationEnum';

import { VideoLesson } from '../lesson/VideoLesson';
import { ListenConversation } from './ListenConversation';
import { ListenEssay } from './ListenEssay';
import { ListenGallery } from './ListenGallery';

export const ListenContainer = () => {
  const { paragraph } = useConversationContext();

  return (
    <>
      <div
        className={classNames('flex-1 justify-between flex flex-col w-full', {
          'pl-8': paragraph?.item !== ConversationTypeEnum.GALLERY && paragraph?.item !== ConversationTypeEnum.VIDEO,
        })}
      >
        {paragraph?.item === ConversationTypeEnum.CONVERSATION && <ListenConversation />}
        {paragraph?.item === ConversationTypeEnum.ESSAY && <ListenEssay />}
        {paragraph?.item === ConversationTypeEnum.GALLERY && <ListenGallery />}
        {paragraph?.item === ConversationTypeEnum.VIDEO && <VideoLesson />}
      </div>
    </>
  );
};
