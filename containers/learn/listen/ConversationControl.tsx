'use client';

import { useCallback } from 'react';

import { StatusEnum } from '@/configs/StatusEnum';
import useBalanceStore from '@/store/balance';
import classNames from 'classnames';
import Button from 'components/Button';
import VolumeControl from 'containers/learn/VolumeControl';
import { useTranslations } from 'next-intl';
import { toast } from 'react-hot-toast';
import useSpeakingStore from 'store/speaking';
import { ConversationControlProps } from 'types/component';
import { useEventListener } from 'usehooks-ts';

const ConversationControl = ({
  onNextSentence,
  endSentence,
  isFinishConversation,
  onFinish,
  isLastSentence,
  onLeanAgain,
  onBackSentence,
}: ConversationControlProps) => {
  const { balanceStatus } = useBalanceStore();
  const { autoReading, setAutoReading, replay, setReplay } = useSpeakingStore();
  const t = useTranslations();
  const handleAutoReading = () => {
    if (isLastSentence && !replay) return;
    setAutoReading(!autoReading);
  };
  const handleNextButton = () => {
    if (!autoReading && balanceStatus === StatusEnum.ON) onNextSentence();
  };
  const handleClickReplay = () => {
    // if (!autoReading) return;
    setReplay(!replay);
  };

  const keysPressed: Record<string, boolean> = {};

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      keysPressed[event.key] = true;

      // Shift + Enter: Restart learning
      if (event.shiftKey && event.key === 'Enter') {
        if (isFinishConversation) onLeanAgain();
        return;
      }

      // Enter: Next sentence (if balance is sufficient)
      if (!event.shiftKey && !event.metaKey && !event.ctrlKey && event.key === 'Enter') {
        if (balanceStatus !== StatusEnum.ON) {
          toast.error(t('learn.note_not_enough_token'), {
            duration: 10000,
          });
          return;
        }
        if (!replay && isLastSentence) {
          onFinish();
          return;
        }
        if (!autoReading && !isLastSentence) {
          onNextSentence();
          return;
        }
      }

      // Space: Toggle auto-reading
      if (!event.shiftKey && !event.metaKey && !event.ctrlKey && event.key === ' ') {
        handleAutoReading();
        return;
      }

      // Left Arrow: Go to previous sentence
      if (event.code === 'ArrowLeft') {
        if (endSentence) onBackSentence();
        return;
      }
    },
    [
      balanceStatus,
      autoReading,
      endSentence,
      isFinishConversation,
      isLastSentence,
      onBackSentence,
      onFinish,
      onLeanAgain,
      onNextSentence,
      replay,
      t,
    ]
  );

  const handleKeyUp = useCallback((event: KeyboardEvent) => {
    delete keysPressed[event.key];
  }, []);

  // Register event listeners
  useEventListener('keydown', handleKeyDown, undefined, { capture: true });
  useEventListener('keyup', handleKeyUp, undefined, { capture: true });

  return (
    <div className={'z-50 px-4 text-center flex items-center justify-center h-full'}>
      {!isFinishConversation && (
        <>
          <div className={'flex items-center justify-between w-full'}>
            <VolumeControl />

            <div className={'mt-2 mb-3'}>
              <div className={'flex items-center justify-center'}>
                <div
                  className={classNames('cursor-pointer mr-4', {
                    'opacity-50': autoReading || !endSentence,
                  })}
                  onClick={() => onLeanAgain()}
                >
                  <i
                    className={classNames('icon-start text-base text-color-minor cursor-default', {
                      'hover:text-purple !cursor-pointer': !autoReading && endSentence,
                    })}
                  />
                </div>

                <div
                  className={classNames('cursor-pointer mr-5', {
                    'opacity-50': autoReading || !endSentence,
                  })}
                  onClick={() => onBackSentence()}
                >
                  <i
                    className={classNames(
                      'icon-play-back text-large text-color-minor cursor-default',
                      {
                        'hover:text-purple !cursor-pointer': !autoReading && endSentence,
                      }
                    )}
                  />
                </div>

                <div
                  id={'btn-auto-reading'}
                  onClick={handleAutoReading}
                  className={classNames(
                    'relative cursor-pointer bg-purple flex items-center justify-center w-[50px] h-[50px] rounded-full',
                    {
                      '!bg-bg-box': !autoReading,
                    }
                  )}
                >
                  <i
                    className={classNames('text-color-minor text-2xl icon-play', {
                      '!text-bg-general !hidden': autoReading,
                    })}
                  />
                  <i
                    className={classNames('text-color-minor text-2xl icon-stop', {
                      '!text-bg-general !block': autoReading,
                      '!hidden': !autoReading,
                    })}
                  />
                  <div
                    className={
                      'w-full absolute -bottom-3 text-[0.625rem] left-1/2 -translate-x-1/2'
                    }
                  >
                    Space
                  </div>
                </div>

                <div onClick={handleClickReplay} className={'cursor-pointer ml-6'}>
                  <i
                    className={classNames('icon-loop text-2xl text-color-minor hover:text-purple', {
                      'text-purple': replay,
                    })}
                  />
                </div>
              </div>
            </div>

            <div className={'text-right mt-4'}>
              {isLastSentence && !replay ? (
                <Button color={'primary'} size={'md'} onClick={onFinish} isDisabled={!endSentence}>
                  {t('learn.finish')}
                </Button>
              ) : (
                <Button
                  color={'primary'}
                  size={'md'}
                  onClick={handleNextButton}
                  isDisabled={!endSentence || autoReading || balanceStatus !== StatusEnum.ON}
                >
                  {t('learn.continue')}
                </Button>
              )}
              <div className={'flex justify-end w-[150px]'}>
                <span
                  className={classNames(
                    'inline-block text-[0.625rem] w-[150px] text-right mr-9 mt-1',
                    {
                      '!mr-12': isLastSentence && !replay,
                    }
                  )}
                >
                  Enter
                </span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
export default ConversationControl;
