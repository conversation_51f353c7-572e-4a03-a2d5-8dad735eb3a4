import { useCallback, useEffect, useMemo, useRef, useState } from 'react';



import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { StatusEnum } from '@/configs/StatusEnum';
import { useConversationContext } from '@/providers/ConversationProvider';
import useBalanceStore from '@/store/balance';
import useLearnStore from '@/store/learn';
import classNames from 'classnames';
import ReactPlayer from 'react-player/youtube';



import { useBackSentenceMutation, useEndListenMutation, useSaveCurrentSentenceMutation, useStartListenMutation } from '@/hooks/Ent/useSentence';
import { useSession } from '@/hooks/useSession';



import FinishScreen from '../FinishScreen';
import StartScreen from '../StartScreen';
import LessonVideoControl from './LessonVideoControl';





const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const Timeline = ({
  // sentences,
  // currentIndex,
  currentTime,
  // onSeek,
  totalDuration,
}: {
  // sentences: any[];
  // currentIndex: number;
  currentTime: number;
  // onSeek: (index: number) => void;
  totalDuration: number;
}) => {
  return (
    <div className="absolute bottom-0 left-0 right-0">
      {/* Time display */}
      <div className="absolute bottom-4 left-4 bg-black/70 text-white px-2 py-1 rounded text-sm">
        {formatTime(currentTime)} / {formatTime(totalDuration)}
      </div>
    </div>
  );
};

const SentenceDisplay = ( { sentence, shouldShow }: { sentence: any; shouldShow: boolean } ) =>
{
  if (!sentence || !shouldShow) return null;
  const { setSelectedSentence,showWord } = useLearnStore();

  return (
    <div className={classNames('absolute cursor-pointer left-1/2 -translate-x-1/2 bottom-1 bg-black/50 p-6 rounded-lg text-white max-w-2xl w-full', {
      'opacity-100': showWord,
      'opacity-50': !showWord,
    })}>
      <div
        className="text-xl font-medium text-center"
        onClick={() => setSelectedSentence(sentence)}
      >
        {sentence.content}
      </div>
    </div>
  );
};

export const VideoLesson = () => {
  const { paragraph, sentences, activeTab, setActiveTab, listenCurrentId, isLoadingConversations } =
    useConversationContext();

  // Sort sentences by end field
  const sortedSentences = useMemo(() => {
    if (!sentences || sentences.length === 0) return [];
    return [...sentences].sort((a, b) => ((a as any)?.end || 0) - ((b as any)?.end || 0));
  }, [sentences]);

  const { exerciseToken, transactionInfo } = useLearnStore();
  const { balanceStatus } = useBalanceStore();
  const { data: session } = useSession();
  const [currentSentenceIndex, setCurrentSentenceIndex] = useState<number>(0);
  const [isFinished, setIsFinished] = useState<boolean>(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [isStartedLearn, setIsStartedLearn] = useState<boolean>(false);
  const [playedSentences, setPlayedSentences] = useState<Set<number>>(new Set());
  const [shouldShowSentence, setShouldShowSentence] = useState<boolean>(false);
  const [videoError, setVideoError] = useState<boolean>(false);
  const [pauseRequested, setPauseRequested] = useState<boolean>(false);
  const [playCurrentToEnd, setPlayCurrentToEnd] = useState<boolean>(false);
  const [isFinishingCurrentSentence, setIsFinishingCurrentSentence] = useState<boolean>(false);
  const [actualVideoDuration, setActualVideoDuration] = useState<number>(0);
  const [isInPostSentencesPeriod, setIsInPostSentencesPeriod] = useState<boolean>(false);
  const [isManuallyPlayingInPostSentences, setIsManuallyPlayingInPostSentences] =
    useState<boolean>(false);
  const playerRef = useRef<ReactPlayer>(null);
  const isManualTimeSetting = useRef<boolean>(false);
  const [autoReading, setAutoReading] = useState<boolean>(false);
  const [replay, setReplay] = useState<boolean>(false);
  const timerRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const startListenMutation = useStartListenMutation();
  const endListenMutation = useEndListenMutation();
  const saveCurrentSentenceMutation = useSaveCurrentSentenceMutation();
  const currentSentence = sortedSentences[currentSentenceIndex];

  // Video URL with fallback
  const videoUrl = useMemo(() => {
    if (videoError) {
      return 'https://www.youtube.com/watch?v=NI84uLbFWC4&list=RDNI84uLbFWC4&start_radio=1'; // Your fixed fallback video
    }
    return (
      (paragraph as any)?.link ||
      'https://www.youtube.com/watch?v=NI84uLbFWC4&list=RDNI84uLbFWC4&start_radio=1'
    );
  }, [paragraph, videoError]);

  // State to trigger duration recalculation
  const [durationTrigger, setDurationTrigger] = useState(0);

  // Force duration recalculation by updating trigger state
  const triggerDurationUpdate = useCallback(() => {
    setDurationTrigger((prev) => prev + 1);
  }, []);

  // Find current sentence index based on current time and start times
  const findCurrentSentenceIndex = useCallback(
    (time: number) => {
      if (!sortedSentences || sortedSentences.length === 0) return 0;

      // Find the appropriate sentence for the current time
      for (let i = 0; i < sortedSentences.length; i++) {
        const sentence = sortedSentences[i];
        const startTime = (sentence as any)?.start || 0;
        const endTime = (sentence as any)?.end || 0;

        // If we're within this sentence's time range, return its index
        if (time >= startTime && time <= endTime) {
          return i;
        }

        // If we're before this sentence starts, check if we're in a gap
        if (time < startTime) {
          // If this is the first sentence and we're before it, stay at index 0
          if (i === 0) {
            return 0;
          }
          // We're in a gap between sentences, return the previous sentence index
          // This ensures we maintain context but don't show content during gaps
          return i - 1;
        }
      }

      // If we're past all sentences, return the last sentence index
      return sortedSentences.length - 1;
    },
    [sortedSentences]
  );

  // Check if current sentence content should be displayed
  const checkShouldShowSentence = useCallback(
    (time: number, sentenceIndex: number) => {
      if (!sortedSentences || sentenceIndex >= sortedSentences.length) return false;

      const sentence = sortedSentences[sentenceIndex];
      const startTime = (sentence as any)?.start || 0;
      const endTime = (sentence as any)?.end || 0;

      // Only show sentence during its actual time range
      const shouldShow = time >= startTime && time <= endTime;

      // Debug logging for gap times
      if (!shouldShow && time > endTime) {
        const nextSentence = sortedSentences[sentenceIndex + 1];
        const nextStartTime = nextSentence ? (nextSentence as any)?.start || 0 : null;

        if (nextStartTime && time < nextStartTime) {
          console.log(
            `Gap time detected: ${time}s (between sentence ${sentenceIndex} ending at ${endTime}s and sentence ${sentenceIndex + 1} starting at ${nextStartTime}s)`
          );
        }
      }

      return shouldShow;
    },
    [sortedSentences]
  );

  // Initialize player position based on listenCurrentId
  useEffect(() => {
    if (listenCurrentId && sortedSentences && sortedSentences.length > 0) {
      const matchedSentenceIndex = sortedSentences.findIndex(
        (sentence) => sentence.id === listenCurrentId
      );
      if (matchedSentenceIndex !== -1) {
        const matchedSentence = sortedSentences[matchedSentenceIndex];
        const startTime = (matchedSentence as any)?.start || 0;

        setCurrentSentenceIndex(matchedSentenceIndex);
        setCurrentTime(startTime);

        // Seek video player to the matched sentence position with retry logic
        const seekToPosition = () => {
          if (playerRef.current) {
            playerRef.current.seekTo(startTime, 'seconds');

            // Verify the seek worked after a short delay
            // setTimeout(() => {
            //   if (playerRef.current) {
            //     const currentVideoTime = playerRef.current.getCurrentTime();
            //     console.log('Video current time after seek:', currentVideoTime);
            //     console.log('Target time was:', startTime);
            //   }
            // }, 500);
          } else {
            setTimeout(seekToPosition, 100);
          }
        };

        seekToPosition();

        // Turn off auto-play when seeking to specific sentence
        setAutoReading(false);
        setIsPlaying(false);

        // Immediately update sentence display for the new position
        const shouldShow = checkShouldShowSentence(startTime, matchedSentenceIndex);
        setShouldShowSentence(shouldShow);

        // Trigger duration adaptation for new position
        setTimeout(() => triggerDurationUpdate(), 100);
      }
    }
  }, [
    listenCurrentId,
    sortedSentences,
    triggerDurationUpdate,
    setAutoReading,
    checkShouldShowSentence,
  ]);

  // Calculate total video duration from the maximum end time of all sentences
  const totalVideoDuration = useMemo(() => {
    if (sortedSentences && sortedSentences.length > 0) {
      const maxSentenceEndTime = Math.max(
        ...sortedSentences.map((sentence) => (sentence as any)?.end || 0)
      );
      const sentencesDuration = maxSentenceEndTime > 0 ? maxSentenceEndTime : 120;

      // Calculate dynamic duration based on current context
      let contextualDuration = sentencesDuration;

      // If we have actual video duration, consider it
      if (actualVideoDuration > 0) {
        // If user is at the beginning or restarted, use full video duration potential
        if (currentTime <= 10 || currentSentenceIndex === 0) {
          contextualDuration = Math.max(actualVideoDuration, sentencesDuration);
        }
        // If user is in the middle, adapt based on remaining content
        else {
          const remainingSentenceTime = maxSentenceEndTime - currentTime;
          const remainingVideoTime = actualVideoDuration - currentTime;

          // Use the longer of remaining times, but add current time as base
          contextualDuration = currentTime + Math.max(remainingSentenceTime, remainingVideoTime);
        }
      }

      return contextualDuration;
    }
    const fallbackDuration = actualVideoDuration > 0 ? actualVideoDuration : 120;
    return fallbackDuration;
  }, [sortedSentences, actualVideoDuration, currentTime, currentSentenceIndex, durationTrigger]);

  // Handle content timing
  useEffect(() => {
    if (isPlaying && !isManualTimeSetting.current) {
      timerRef.current = setInterval(() => {
        setCurrentTime((prev) => {
          const newTime = prev + 1;

          // Check if current sentence has ended and save it
          const currentSentence = sortedSentences[currentSentenceIndex];
          if (currentSentence && !playedSentences.has(currentSentence.id)) {
            const sentenceEndTime = (currentSentence as any)?.end || 0;
            if (newTime >= sentenceEndTime) {
              saveCurrentSentenceMutation.mutate({
                document_id: paragraph?.document_id || 0,
                paragraph_id: paragraph?.id || 0,
                sentence_ids: [currentSentence.id],
                member_id: session?.user.id,
                member_exercise_token: exerciseToken,
              });
              setPlayedSentences((prev) => new Set([...prev, currentSentence.id]));
            }
          }

          // Update sentence index based on start times (video plays continuously)
          const newIndex = findCurrentSentenceIndex(newTime);
          if (newIndex !== currentSentenceIndex && newIndex < sortedSentences.length) {
            setCurrentSentenceIndex(newIndex);
          }

          // Check if sentence content should be displayed
          const shouldShow = checkShouldShowSentence(newTime, newIndex);
          setShouldShowSentence(shouldShow);

          // Check if we should pause at the end of current sentence when playCurrentToEnd is requested
          if (playCurrentToEnd && sortedSentences[currentSentenceIndex]) {
            const currentSentenceEnd = (sortedSentences[currentSentenceIndex] as any)?.end || 0;

            if (newTime >= currentSentenceEnd) {
              // Always pause at sentence end when playCurrentToEnd is requested
              setIsPlaying(false);
              setPlayCurrentToEnd(false);
              setAutoReading(false);
              setIsFinishingCurrentSentence(false);
              return newTime;
            }
          }

          // Check if we should pause at the end of current sentence
          if (pauseRequested && newIndex !== currentSentenceIndex) {
            // We've moved to a new sentence, so pause now
            setIsPlaying(false);
            setPauseRequested(false);
            setIsFinishingCurrentSentence(false);
            return newTime;
          }

          // Handle end of content - this comes AFTER sentence-specific checks
          if (newTime >= totalVideoDuration) {
            clearInterval(timerRef.current);
            setIsPlaying(false);
            setPauseRequested(false);
            setPlayCurrentToEnd(false);
            if (replay) {
              setCurrentTime(0);
              setCurrentSentenceIndex(0);
              setShouldShowSentence(false);
              // Remove auto-playing on loop
              // setIsPlaying(true);
            } else {
              setIsFinished(true);
            }
            return 0;
          }

          return newTime;
        });
      }, 1000);
    } else {
      clearInterval(timerRef.current);
    }

    return () => {
      clearInterval(timerRef.current);
    };
  }, [
    isPlaying,
    currentSentenceIndex,
    sortedSentences,
    paragraph,
    session,
    exerciseToken,
    saveCurrentSentenceMutation,
    playedSentences,
    replay,
    findCurrentSentenceIndex,
    totalVideoDuration,
    checkShouldShowSentence,
    pauseRequested,
    playCurrentToEnd,
  ]);

  // Handle auto-reading state changes
  useEffect(() => {
    if (autoReading) {
      // Always set playing when autoReading is true, regardless of post-sentences period
      setIsPlaying(true);
      setPauseRequested(false);
      setIsFinishingCurrentSentence(false);
    } else {
      // When auto-reading is turned off
      if (isInPostSentencesPeriod) {
        // In post-sentences period, respect manual play state
        if (isManuallyPlayingInPostSentences) {
          return;
        }

        // Use a small timeout to allow manual state changes to complete
        setTimeout(() => {
          setIsPlaying(false);
          setPauseRequested(false);
          setIsFinishingCurrentSentence(false);
          // Clear the timer when pausing in post-sentences period
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
        }, 50);
      } else {
        // Original logic for sentence-based pausing
        // Mark that pause is requested but don't stop playing immediately - let the sentence finish
        setPauseRequested(true);
        // If currently playing, mark that we're finishing the current sentence
        if (isPlaying) {
          setIsFinishingCurrentSentence(true);
        }
      }
    }
  }, [autoReading, isPlaying, isInPostSentencesPeriod, isManuallyPlayingInPostSentences]);

  // const handleSeek = useCallback(
  //   (index: number) => {
  //     setCurrentSentenceIndex(index);
  //     const seekTime = (sortedSentences[index] as any)?.start || 0;
  //     setCurrentTime(seekTime);

  //     // Seek video player to the target position
  //     if (playerRef.current) {
  //       playerRef.current.seekTo(seekTime, 'seconds');
  //     }

  //     // Clean up post-sentences related states when seeking
  //     setIsInPostSentencesPeriod(false);
  //     setIsManuallyPlayingInPostSentences(false);
  //     setPlayCurrentToEnd(false);
  //     setPauseRequested(false);
  //     setIsFinishingCurrentSentence(false);

  //     // Immediately update sentence display for the new position
  //     const shouldShow = checkShouldShowSentence(seekTime, index);
  //     setShouldShowSentence(shouldShow);

  //     // Trigger duration recalculation for new position
  //     setTimeout(() => triggerDurationUpdate(), 100);

  //     if (autoReading) {
  //       setIsPlaying(true);
  //     }
  //   },
  //   [autoReading, sortedSentences, triggerDurationUpdate, checkShouldShowSentence]
  // );

  const handleNextSentence = useCallback(() => {
    // Don't allow next sentence while current sentence is finishing
    if (isFinishingCurrentSentence) {
      return;
    }

    const currentSentence = sortedSentences[currentSentenceIndex];
    const currentSentenceEnd = (currentSentence as any)?.end || 0;

    // If we're not at the end of current sentence, play to the end
    if (currentTime < currentSentenceEnd) {
      setAutoReading(false);
      // Play current sentence to the end and pause
      setIsPlaying(true);
      setPlayCurrentToEnd(true);
      setPauseRequested(false); // Clear any pause request to allow manual play
      // Reset post-sentences manual play flag since we're now playing a specific sentence
      setIsManuallyPlayingInPostSentences(false);
      return;
    }

    // We're at the end of current sentence, move to next sentence
    // Set flag to prevent timer from interfering
    isManualTimeSetting.current = true;

    // Stop the timer first to prevent auto-advancing
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setIsPlaying(false);
    setAutoReading(false);
    setPlayCurrentToEnd(false);
    setPauseRequested(false);
    setIsFinishingCurrentSentence(false);

    // Save current sentence if it hasn't been played before
    if (currentSentence && !playedSentences.has(currentSentence.id)) {
      saveCurrentSentenceMutation.mutate({
        document_id: paragraph?.document_id || 0,
        paragraph_id: paragraph?.id || 0,
        sentence_ids: [currentSentence.id],
        member_id: session?.user.id,
        member_exercise_token: exerciseToken,
      });
      setPlayedSentences((prev) => new Set([...prev, currentSentence.id]));
    }

    // Special case: if at time 0, go to first sentence start
    if (currentTime === 0) {
      const firstSentenceStart = (sortedSentences[0] as any)?.start || 0;
      setCurrentTime(firstSentenceStart);
      setCurrentSentenceIndex(0);

      // Seek video player to first sentence
      if (playerRef.current) {
        playerRef.current.seekTo(firstSentenceStart, 'seconds');
      }

      // Auto-play the first sentence to the end
      setIsPlaying(true);
      setAutoReading(false);
      setPlayCurrentToEnd(true);
      // Reset post-sentences manual play flag since we're now playing a specific sentence
      setIsManuallyPlayingInPostSentences(false);
    }
    // Check if we're at the last sentence
    else if (currentSentenceIndex === sortedSentences.length - 1) {
      // We're at the last sentence and at its end
      const lastSentence = sortedSentences[sortedSentences.length - 1];
      const lastSentenceEndTime = (lastSentence as any)?.end || 0;

      // Check if there's remaining video duration after the last sentence
      if (lastSentenceEndTime < totalVideoDuration && !replay) {
        // There's remaining video time - continue playing into post-sentences period
        setCurrentTime(lastSentenceEndTime);

        // Seek video player to the end of last sentence
        if (playerRef.current) {
          playerRef.current.seekTo(lastSentenceEndTime, 'seconds');
        }

        setIsPlaying(true);
        setAutoReading(false); // Set to true so the timer continues and doesn't get paused by the effect
        // Don't set playCurrentToEnd since we want to continue to video end
        setPlayCurrentToEnd(false);
        // Set flag to indicate user manually wants to play in post-sentences period
        setIsManuallyPlayingInPostSentences(true);
      } else if (replay) {
        // Replay logic
        setCurrentSentenceIndex(0);
        setCurrentTime(0);

        // Seek video player back to beginning for replay
        if (playerRef.current) {
          playerRef.current.seekTo(0, 'seconds');
        }

        setIsFinished(false);
        setIsPlaying(false);
      } else {
        // No remaining video time or replay disabled - finish the lesson
        if (transactionInfo) {
          endListenMutation.mutate({
            transactionInfo: transactionInfo,
          });
        }
        setIsFinished(true);
        setIsPlaying(false);
        setCurrentTime(totalVideoDuration);
      }
    } else {
      // Move to next sentence and auto-play it to the end
      const nextIndex = currentSentenceIndex + 1;
      const nextSentenceStart = (sortedSentences[nextIndex] as any)?.start || 0;
      setCurrentSentenceIndex(nextIndex);
      setCurrentTime(nextSentenceStart);

      // Seek video player to next sentence
      if (playerRef.current) {
        playerRef.current.seekTo(nextSentenceStart, 'seconds');
      }

      // Auto-play the next sentence to the end
      setIsPlaying(true);
      setAutoReading(false);
      setPlayCurrentToEnd(true);
      // Reset post-sentences manual play flag since we're now playing a specific sentence
      setIsManuallyPlayingInPostSentences(false);
    }

    // Reset flag to allow timer to start
    isManualTimeSetting.current = false;
  }, [
    currentSentenceIndex,
    sortedSentences,
    currentTime,
    paragraph,
    session,
    exerciseToken,
    saveCurrentSentenceMutation,
    playedSentences,
    setAutoReading,
    replay,
    transactionInfo,
    endListenMutation,
    totalVideoDuration,
    isFinishingCurrentSentence,
  ]);

  const handleVideoDuration = useCallback(
    (duration: number) => {
      setActualVideoDuration(duration);

      // Force pause when video loads, especially for fallback videos
      if (playerRef.current && !isPlaying) {
        setTimeout(() => {
          if (playerRef.current) {
            try {
              const internalPlayer = playerRef.current.getInternalPlayer();
              if (internalPlayer) {
                if (typeof internalPlayer.pauseVideo === 'function') {
                  internalPlayer.pauseVideo();
                }
                if (typeof internalPlayer.stopVideo === 'function') {
                  internalPlayer.stopVideo();
                }
              }
            } catch (error) {
              console.log('🟢 Error pausing video after duration load:', error);
            }
          }
        }, 100);
      }
    },
    [videoUrl, videoError, isPlaying]
  );

  const backSentenceMutation = useBackSentenceMutation();

  const handleBackSentence = useCallback(() => {
    // Don't allow back while current sentence is finishing
    if (isFinishingCurrentSentence) return;

    let targetIndex = currentSentenceIndex;
    let targetTime = 0;

    // Case 1: At end of current sentence - go to start of current sentence
    if (currentTime === sortedSentences[currentSentenceIndex].end) {
      targetTime = sortedSentences[currentSentenceIndex].start || 0;
    }
    // Case 2: Not at end of current sentence - go to start of previous sentence
    else if (currentSentenceIndex > 0) {
      targetIndex = currentSentenceIndex - 1;
      targetTime = sortedSentences[targetIndex].start || 0;
    }
    // Edge case: At first sentence and not at end - go to start of current sentence
    else {
      targetTime = sortedSentences[currentSentenceIndex].start || 0;
    }

    // Common logic for both cases
    setCurrentTime(targetTime);

    // Update sentence index if it changed
    if (targetIndex !== currentSentenceIndex) {
      setCurrentSentenceIndex(targetIndex);
    }

    // Seek video player to the new position
    if (playerRef.current) {
      playerRef.current.seekTo(targetTime, 'seconds');
    }

    // Save current sentence (adjust ID based on target index)
    backSentenceMutation.mutate({
      activeTab: LearnTypeEnum.LISTEN,
      sentence_id: currentSentence.id - (currentSentenceIndex - targetIndex),
      paragraph_id: paragraph?.id || 0,
      member_exercise_token: exerciseToken || '',
    });

    // Common state resets
    setIsFinishingCurrentSentence(false);
    setIsInPostSentencesPeriod(false);
    setIsManuallyPlayingInPostSentences(false);
    setPlayCurrentToEnd(false);
    setPauseRequested(false);
    setIsPlaying(false);
    setAutoReading(false);

    // Update sentence display for the new position
    const shouldShow = checkShouldShowSentence(targetTime, targetIndex);
    setShouldShowSentence(shouldShow);

    // Trigger duration recalculation for new position
    setTimeout(() => triggerDurationUpdate(), 100);
  }, [
    currentSentenceIndex,
    currentTime,
    sortedSentences,
    isFinishingCurrentSentence,
    triggerDurationUpdate,
    checkShouldShowSentence,
  ]);

  const handleFinish = useCallback(() => {
    if (replay) {
      setCurrentSentenceIndex(0);
      setCurrentTime(0);
      setIsFinished(false);
      setIsPlaying(false);

      // Seek video player back to beginning for replay
      if (playerRef.current) {
        playerRef.current.seekTo(0, 'seconds');
      }

      return;
    }

    if (transactionInfo) {
      endListenMutation.mutate({
        transactionInfo: transactionInfo,
      });
    }
    setIsFinished(true);
    setIsPlaying(false);
    setCurrentTime(totalVideoDuration);
  }, [replay, transactionInfo, endListenMutation, totalVideoDuration]);

  const handleLearnAgain = useCallback(() => {
    setCurrentSentenceIndex(0);
    setIsFinished(false);
    setCurrentTime(0);
    setIsPlaying(false);
    setIsFinishingCurrentSentence(false);

    // Reset all video-related states
    setVideoError(false);
    setPauseRequested(false);
    setPlayCurrentToEnd(false);
    setPlayedSentences(new Set());
    setIsInPostSentencesPeriod(false);
    setIsManuallyPlayingInPostSentences(false);

    // Turn off auto-play when learning again
    setAutoReading(false);

    // Seek video player back to the beginning and ensure it's paused
    if (playerRef.current) {
      playerRef.current.seekTo(0, 'seconds');

      // Multiple pause strategies for better reliability
      try {
        const internalPlayer = playerRef.current.getInternalPlayer();

        if (internalPlayer) {
          // Strategy 1: Try standard pause method
          if (typeof internalPlayer.pause === 'function') {
            internalPlayer.pause();
          }

          // Strategy 2: YouTube-specific pause methods
          if (typeof internalPlayer.pauseVideo === 'function') {
            internalPlayer.pauseVideo();
          }

          if (typeof internalPlayer.stopVideo === 'function') {
            internalPlayer.stopVideo();
          }

          // Strategy 3: Set player state directly if available
          if (
            internalPlayer.getPlayerState &&
            typeof internalPlayer.getPlayerState === 'function'
          ) {
            const playerState = internalPlayer.getPlayerState();

            // YouTube player states: -1 (unstarted), 0 (ended), 1 (playing), 2 (paused), 3 (buffering), 5 (cued)
            if (playerState === 1) {
              // Currently playing
              internalPlayer.pauseVideo();
            }
          }
        }
      } catch (error) {
        console.log('🟢 Error pausing internal player:', error);
      }

      // Additional safety: Force ReactPlayer playing prop to false
      // This ensures the ReactPlayer component itself knows it should be paused
      setTimeout(() => {
        if (playerRef.current) {
          // Double-check the internal player is still paused
          try {
            const internalPlayer = playerRef.current.getInternalPlayer();
            if (internalPlayer && typeof internalPlayer.pauseVideo === 'function') {
              internalPlayer.pauseVideo();
            }
          } catch (error) {
            console.log('🟢 Error in delayed pause:', error);
          }
        }
      }, 100);
    }

    // Immediately update sentence display for the beginning position
    const shouldShow = checkShouldShowSentence(0, 0);
    setShouldShowSentence(shouldShow);

    // Trigger duration recalculation for learn again
    setTimeout(() => triggerDurationUpdate(), 100);
  }, [triggerDurationUpdate, checkShouldShowSentence, setAutoReading]);

  const handleStartLearn = useCallback(() => {
    if (!paragraph || balanceStatus !== StatusEnum.ON) return;
    setIsStartedLearn(true);
    setIsPlaying(false);
    setAutoReading(false);
    startListenMutation.mutate({
      document_id: paragraph?.document_id || 0,
      paragraph_id: paragraph?.id || 0,
      course_id: paragraph?.course_id || 0,
      activeTab: LearnTypeEnum.LISTEN,
      member_exercise_token: exerciseToken || '',
    });
  }, [paragraph, balanceStatus, exerciseToken, startListenMutation]);

  const handleRestart = useCallback(() => {
    setCurrentSentenceIndex(0);
    setIsFinished(false);
    setCurrentTime(0);
    setIsStartedLearn(false);
    setIsPlaying(false);
    setAutoReading(false);
    setIsFinishingCurrentSentence(false);

    // Reset all video-related states
    setVideoError(false);
    setPauseRequested(false);
    setPlayCurrentToEnd(false);
    setPlayedSentences(new Set());
    setIsInPostSentencesPeriod(false);
    setIsManuallyPlayingInPostSentences(false);

    // Seek video player back to the beginning AND pause it
    if (playerRef.current) {
      playerRef.current.seekTo(0, 'seconds');
      setIsPlaying(false);
      // Add explicit pause - this depends on your video player library
      if (playerRef.current.getInternalPlayer) {
        playerRef.current.getInternalPlayer().pause();
      }
    }

    // Immediately update sentence display for the beginning position
    const shouldShow = checkShouldShowSentence(0, 0);
    setShouldShowSentence(shouldShow);

    // Trigger duration recalculation for restart
    setTimeout(() => triggerDurationUpdate(), 100);
  }, [triggerDurationUpdate, checkShouldShowSentence]);

  useEffect(() => {
    if (currentTime === 0 && replay) {
      setAutoReading(false);
    }
    // Only run when currentTime or replay changes
  }, [currentTime, replay, setAutoReading]);

  const handleVideoError = useCallback(() => {
    setVideoError(true);
    setIsPlaying(false); // Ensure playing state is false when switching to fallback
  }, []);

  const handleVideoReady = useCallback(() => {
    if (!isPlaying && playerRef.current) {
      setTimeout(() => {
        if (playerRef.current) {
          try {
            const internalPlayer = playerRef.current.getInternalPlayer();
            if (internalPlayer) {
              if (typeof internalPlayer.pauseVideo === 'function') {
                internalPlayer.pauseVideo();
              }
              if (typeof internalPlayer.stopVideo === 'function') {
                internalPlayer.stopVideo();
              }
            }
          } catch (error) {
            console.log('🟢 Error pausing video on ready:', error);
          }
        }
      }, 100);
    }
  }, [isPlaying]);

  // Check if we're truly at the end (last sentence AND at its end time)
  const isAtEndOfLastSentence = useMemo(() => {
    const isOnLastSentence = currentSentenceIndex === sortedSentences.length - 1;
    if (!isOnLastSentence || !currentSentence) return false;

    const sentenceEndTime = (currentSentence as any)?.end || 0;
    const isAtSentenceEnd = currentTime >= sentenceEndTime;

    // If we're in post-sentences period, return false to allow space key functionality
    if (isAtSentenceEnd && isInPostSentencesPeriod) {
      return false;
    }

    return isAtSentenceEnd;
  }, [
    currentSentenceIndex,
    sortedSentences.length,
    currentSentence,
    currentTime,
    isInPostSentencesPeriod,
  ]);

  // Check if we're in post-sentences period (all sentences finished but video still has time)
  const checkIsInPostSentencesPeriod = useMemo(() => {
    if (!sortedSentences || sortedSentences.length === 0) return false;

    const lastSentence = sortedSentences[sortedSentences.length - 1];
    const lastSentenceEndTime = (lastSentence as any)?.end || 0;

    // Don't consider it post-sentences period if we're actively playing current sentence to end
    if (playCurrentToEnd) {
      return false;
    }

    // We're in post-sentences period if current time is at or past the last sentence end time
    // but before the total video duration
    return currentTime >= lastSentenceEndTime && currentTime < totalVideoDuration;
  }, [sortedSentences, currentTime, totalVideoDuration, playCurrentToEnd]);

  // Update post-sentences period state
  useEffect(() => {
    setIsInPostSentencesPeriod(checkIsInPostSentencesPeriod);
  }, [checkIsInPostSentencesPeriod]);

  // Toggle pause/play during post-sentences period
  const handlePostSentencesPauseToggle = useCallback(() => {
    if (!isInPostSentencesPeriod) return;

    const newIsPlaying = !isPlaying;

    if (newIsPlaying) {
      // When resuming play, set autoReading to true so the timer starts
      setAutoReading(true);
      setIsPlaying(true);
      setIsManuallyPlayingInPostSentences(true);
    } else {
      // When pausing, stop the timer and turn off auto-reading
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      setAutoReading(false);
      setIsPlaying(false);
      setIsManuallyPlayingInPostSentences(false);
    }
  }, [isInPostSentencesPeriod, isPlaying, setAutoReading]);

  // Handle keyboard events for post-sentences period
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle space key during post-sentences period
      if (event.code === 'Space' && isInPostSentencesPeriod) {
        event.preventDefault();
        handlePostSentencesPauseToggle();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isInPostSentencesPeriod, handlePostSentencesPauseToggle]);

  if (!isStartedLearn) {
    return (
      <StartScreen
        activeTab={activeTab}
        onStart={handleStartLearn}
        setSelectedCharacter={() => {}}
        selectedCharacter={null}
        isLoadingConversation={isLoadingConversations}
      />
    );
  }

  if (isFinished) {
    return (
      <FinishScreen
        handleRestart={handleRestart}
        handleDoNewExercise={() => setActiveTab(LearnTypeEnum.EXERCISE)}
        isExercise={false}
      />
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh_-100px)] w-full overflow-hidden">
      <div className="flex-1 w-full flex justify-center items-center bg-[#2a2d3c] rounded-[5px] min-h-0">
        <div className="w-full h-full flex flex-col items-center justify-center relative">
          <div className="flex-1 w-full flex items-center justify-center">
            <ReactPlayer
              ref={playerRef}
              url={videoUrl}
              controls={false}
              width="100%"
              height="100%"
              playing={isPlaying}
              style={{
                pointerEvents: 'none',
              }}
              config={{
                playerVars: {
                  autoplay: 0,
                  showinfo: 0,
                  modestbranding: 1,
                  controls: 0,
                  rel: 0,
                  iv_load_policy: 3,
                  start: 0,
                  disablekb: 1,
                  fs: 0,
                  playsinline: 1,
                },
              }}
              onError={handleVideoError}
              onDuration={handleVideoDuration}
              onReady={handleVideoReady}
              onPlay={() => {
                if (!isPlaying) {
                  if (playerRef.current) {
                    try {
                      const internalPlayer = playerRef.current.getInternalPlayer();
                      if (internalPlayer && typeof internalPlayer.pauseVideo === 'function') {
                        internalPlayer.pauseVideo();
                      }
                    } catch (error) {
                      console.log('🟢 Error forcing pause on unwanted play:', error);
                    }
                  }
                }
              }}
            />
          </div>
          {/* Show pause/play indicator during post-sentences period */}
          {isInPostSentencesPeriod && (
            <div
              className="absolute inset-0 flex items-center justify-center cursor-pointer"
              onClick={handlePostSentencesPauseToggle}
            >
              <div className="bg-black/50 rounded-full p-4 pointer-events-none">
                <i
                  className={`text-white text-4xl ${isPlaying ? 'icon-pause' : 'icon-play'}`}
                  style={{ opacity: 0.7 }}
                />
              </div>
            </div>
          )}
          <Timeline
            // sentences={sortedSentences}
            // currentIndex={currentSentenceIndex}
            currentTime={currentTime}
            // onSeek={handleSeek}
            totalDuration={totalVideoDuration}
          />
          <div className="flex-shrink-0 w-full">
            <SentenceDisplay sentence={currentSentence} shouldShow={shouldShowSentence} />
          </div>
        </div>
      </div>

      {sortedSentences && sortedSentences.length > 0 && (
        <div className="flex-shrink-0 w-full p-2">
          <div className="w-full">
            <LessonVideoControl
              onNextSentence={handleNextSentence}
              endSentence={!isFinishingCurrentSentence}
              isFinishConversation={isFinished}
              onFinish={handleFinish}
              isLastSentence={isAtEndOfLastSentence}
              onLeanAgain={handleLearnAgain}
              onBackSentence={handleBackSentence}
              autoReading={autoReading}
              setAutoReading={setAutoReading}
              replay={replay}
              setReplay={setReplay}
            />
          </div>
        </div>
      )}
    </div>
  );
};