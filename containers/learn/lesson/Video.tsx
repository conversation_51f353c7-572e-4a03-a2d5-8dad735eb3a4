'use client';

import { useCallback, useMemo, useState } from 'react';

import { useConversationContext } from '@/providers/ConversationProvider';
import ReactPlayer from 'react-player/youtube';

export const Video = () => {
  const { paragraph } = useConversationContext();
  const [videoError, setVideoError] = useState<boolean>(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);

  if (!paragraph) return null;

  // Video URL with fallback
  const videoUrl = useMemo(() => {
    if (videoError) {
      return 'https://www.youtube.com/watch?v=82OhhGnenxw'; // Fallback video
    }
    return (paragraph as any)?.link || 'https://www.youtube.com/watch?v=82OhhGnenxw';
  }, [paragraph, videoError]);

  const handleVideoError = useCallback(() => {
    console.log('Video failed to load, using fallback');
    setVideoError(true);
  }, []);

  const togglePlayPause = useCallback(() => {
    setIsPlaying((prev) => !prev);
  }, []);

  return (
    <div className="flex flex-col h-[calc(100vh_-120px)] w-full overflow-hidden">
      <div className="flex-1 w-full flex justify-center items-center bg-[#2a2d3c] rounded-[5px] min-h-0">
        <div className="w-full h-full flex items-center justify-center relative">
          <ReactPlayer
            url={videoUrl}
            controls={false}
            width="100%"
            height="100%"
            playing={isPlaying}
            config={{
              playerVars: {
                showinfo: 0,
                modestbranding: 1,
                controls: 0,
                rel: 0,
                iv_load_policy: 3,
              },
            }}
            onError={handleVideoError}
          />

          {/* Play/Pause Overlay Button */}
          <div
            className="absolute inset-0 flex items-center justify-center cursor-pointer"
            onClick={togglePlayPause}
          >
            <div className="bg-black/50 rounded-full p-4 hover:bg-black/70 transition-all duration-200">
              {isPlaying ? (
                // Pause Icon
                <svg className="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" />
                </svg>
              ) : (
                // Play Icon
                <svg className="w-12 h-12 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z" />
                </svg>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
