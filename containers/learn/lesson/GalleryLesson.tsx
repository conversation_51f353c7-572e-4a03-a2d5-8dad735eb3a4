import { useEffect, useMemo, useState } from 'react';

import { Button } from '@/components';
import { SPECIAL_DOCUMENT_ID } from '@/configs';
import { RoleEnum } from '@/configs/RoleEnum';
import { IMAGE_SIZE } from '@/constant';
import { EditGallery } from '@/containers/gallery/EditGallery';
import { getResizeImageUrl } from '@/helpers';
import { sortSentencesByPosition } from '@/helpers/sentence';
import { SentenceGroup } from '@/interfaces';
import { useConversationContext } from '@/providers/ConversationProvider';
import { Skeleton } from '@heroui/react';
import { AxiosError } from 'axios';
import classNames from 'classnames';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { toast } from 'react-hot-toast';

import DragContext from '@/components/Drags/DragContext';
import DragGalleryItem from '@/components/Drags/DragGalleryItem';
import NextImageWithFallback from '@/components/NextImageWithFallback';
import ScrollArea from '@/components/ScrollArea';
import { Tooltip } from '@/components/Tooltip';

import { useUpdateSentenceGroupPosition } from '@/hooks/Ent/useSentenceGroups';

import ModalCreateSentenceGroup from '../ModalCreateSentenceGroup';
import { GalleryLessonItem } from './GalleryLessonItem';

export const GalleryLesson = () => {
  const { data: session } = useSession();
  const t = useTranslations();
  const {
    paragraph,
    isUserValidToAccess,
    sentenceGroups,
    isLoadingSentenceGroup,
    isLoadingConversations,
    sentences,
  } = useConversationContext();
  const [open, setOpen] = useState(false);

  const [dragItems, setDragItems] = useState<SentenceGroup[]>([]);
  const [prevSentenceGroupId, setPrevSentenceGroupId] = useState<number>(0);
  const sortedSentenceGroup = useMemo(
    () =>
      sentenceGroups ? sentenceGroups.sort((a, b) => (a.position ?? 0) - (b.position ?? 0)) : [],
    [sentenceGroups]
  );

  useEffect(() => {
    if (sortedSentenceGroup.length > 0) {
      setDragItems(sortedSentenceGroup);
    }
  }, [sortedSentenceGroup]);

  const [activeSentenceGroup, setActiveSentenceGroup] = useState<SentenceGroup | null>(null);

  useEffect(() => {
    if (sortedSentenceGroup.length > 0 && !activeSentenceGroup) {
      setActiveSentenceGroup(sortedSentenceGroup[0]);
    }
  }, [sortedSentenceGroup]);

  const activeSentences = sentences
    ? sortSentencesByPosition(sentences).filter(
        (sentence) => sentence.sentence_group_id === activeSentenceGroup?.id
      )
    : [];

  const updateSentenceGroupPosition = useUpdateSentenceGroupPosition();

  const handleUpdateSentenceGroupsPosition = async (items: SentenceGroup[]) => {
    try {
      if (items.length === 0) {
        toast.error('No items to update');
        return;
      }
      const positions = items.map((item, index) => ({
        position: index,
        id: item.id,
      }));
      console.log('🚀 ~ handleUpdateSentenceGroupsPosition ~ positions:', positions);
      await updateSentenceGroupPosition.mutateAsync({ sentenceGroups: positions });
      toast.success('Update successfully!');
    } catch (e: unknown) {
      console.log(e);
      toast.error(e instanceof AxiosError ? e.response?.data?.message : t('message.error.unknown'));
    }
  };

  return (
    <ScrollArea
      className={classNames('w-full flex pb-[15px]', {
        'h-[calc(100vh-100px)]': paragraph?.document_id !== SPECIAL_DOCUMENT_ID,
        'h-[calc(100vh-55px)]': paragraph?.document_id === SPECIAL_DOCUMENT_ID,
      })}
      isEnabled={false}
    >
      <ScrollArea
        className={
          'sticky w-[211px] h-full lg:flex flex-col top-0 overflow-y-auto pr-[15px] hidden'
        }
      >
        {paragraph && (
          <ModalCreateSentenceGroup
            activeParagraph={paragraph}
            open={open}
            setOpen={setOpen}
            prevSentenceGroupId={prevSentenceGroupId}
          />
        )}

        <DragContext
          setItems={(items: SentenceGroup[]) => setDragItems(items)}
          items={dragItems}
          onDragEnd={(items: SentenceGroup[]) => {
            setDragItems(items);
            handleUpdateSentenceGroupsPosition(items);
          }}
        >
          {isLoadingSentenceGroup &&
            Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className={'flex flex-col gap-4 mt-4'}>
                <div
                  className={
                    'w-full pl-8 relative h-[87px] flex-shrink-0 rounded-[5px] overflow-hidden'
                  }
                >
                  <Skeleton className="rounded-[5px] h-full">
                    <div className="h-full rounded-[5px] mt-4 bg-secondary" />
                  </Skeleton>
                </div>
              </div>
            ))}
          {!isLoadingSentenceGroup &&
            dragItems.length > 0 &&
            dragItems.map((sentence_group, index) => (
              <DragGalleryItem
                key={sentence_group.id}
                id={sentence_group.id}
                position={index}
                className={'focus:outline-none rounded-[5px]'}
                handleAddItem={() => {
                  setPrevSentenceGroupId(sentence_group.id);
                  setOpen(true);
                }}
                handleAddFirstItem={() => {
                  setPrevSentenceGroupId(0);
                  setOpen(true);
                }}
                onClickItem={() => {
                  setActiveSentenceGroup(sentence_group);
                }}
                dragClassName={index === 0 ? '!top-4' : ''}
                isEnabled={isUserValidToAccess}
              >
                <div className={'w-full h-full flex flex-col'}>
                  <div
                    className={classNames(
                      'w-full relative h-[87px] flex-shrink-0 rounded-[5px] overflow-hidden bg-[#2a2d3c] cursor-pointer',
                      {
                        'border border-solid border-purple':
                          activeSentenceGroup?.id == sentence_group.id,
                      }
                    )}
                  >
                    <NextImageWithFallback
                      src={getResizeImageUrl({
                        keyx: sentence_group.keyx,
                        size: IMAGE_SIZE.THUMBNAIL,
                        created_at: sentence_group.image_time,
                        format: sentence_group.format,
                      })}
                      alt={''}
                      fill
                      priority
                      objectFit={'cover'}
                    />
                  </div>
                </div>
              </DragGalleryItem>
            ))}
          {!isLoadingSentenceGroup && sortedSentenceGroup.length <= 0 && (
            <div className={'mt-4 pl-4'}>
              <Tooltip content={t('learn.createParagraph')}>
                <Button
                  as={'a'}
                  variant={'bordered'}
                  size={'sm'}
                  className="appearance-none rounded-md h-[25px]"
                  onClick={() => {
                    // setPrevSentenceGroupId(0);
                    // setOpenModalCreateSentenceGroup(true);
                  }}
                >
                  <i className={'text-base icon-add !fill-white !stroke-white text-success'} />
                  {t('gallery.add_image_to_gallery')}
                </Button>
              </Tooltip>
            </div>
          )}
        </DragContext>
      </ScrollArea>
      <div
        className={classNames(
          'flex flex-col font-[Helvetica] font-normal pl-[15px] lg:pl-0 space-y-4 relative flex-1 h-full'
        )}
      >
        {isLoadingSentenceGroup && (
          <div
            className={
              'w-full flex flex-col flex-1 bg-bg-general rounded-[5px] overflow-hidden mt-4 pr-4'
            }
          >
            <Skeleton className="rounded-[5px] h-full">
              <div className="h-full rounded-[5px] mt-4 bg-secondary" />
            </Skeleton>
          </div>
        )}
        {!isLoadingSentenceGroup && (
          <>
            {sortedSentenceGroup && sortedSentenceGroup.length > 0 ? (
              <div
                className={
                  'w-full flex flex-col flex-1 bg-bg-general rounded-[5px] overflow-hidden mt-4 pr-4'
                }
              >
                <div className={'relative w-full flex flex-col flex-1 bg-[#2a2d3c] rounded-[5px]'}>
                  <NextImageWithFallback
                    key={activeSentenceGroup?.image}
                    src={activeSentenceGroup?.image}
                    alt={''}
                    fill
                    sizes={'100%'}
                    priority
                    objectFit={'contain'}
                  />
                  {((paragraph?.account_id && paragraph?.account_id === session?.user.id) ||
                    session?.user.role_id === RoleEnum.ROOT) && (
                    <EditGallery
                      sentenceGroup={sentenceGroups[0]}
                      sentences={activeSentences}
                      setIsFetchingSentenceGroupsFirstTime={() => {}}
                    />
                  )}
                </div>
              </div>
            ) : (
              <div className={'w-full flex flex-col flex-1 bg-bg-general rounded-[5px] mt-4 pr-4'}>
                <div
                  className={
                    'relative w-full flex flex-col flex-1 bg-bg-box rounded-[5px] justify-center items-center gap-2 cursor-pointer'
                  }
                  onClick={() => {
                    // setPrevSentenceGroupId(0);
                    // setOpenModalCreateSentenceGroup(true);
                  }}
                >
                  <i className={'icon-image-add-fill text-color-minor text-[72px]'} />
                  <p className={'text-color-minor font-medium text-base'}>
                    {t('gallery.add_image_to_gallery')}
                  </p>
                </div>
              </div>
            )}
          </>
        )}
        {!isLoadingConversations &&
          activeSentences.map((sentence) => {
            return (
              <div
                key={sentence.id}
                className={
                  'w-full flex align-baseline mx-auto px-[46px] absolute left-0 bottom-0 z-10'
                }
              >
                <div className={'w-full h-full px-4 pb-5'}>
                  <GalleryLessonItem sentences={activeSentences} />
                </div>
              </div>
            );
          })}
      </div>
    </ScrollArea>
  );
};
