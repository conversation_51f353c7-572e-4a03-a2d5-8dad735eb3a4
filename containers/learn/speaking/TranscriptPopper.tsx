'use client';

import React, { JSX, useMemo } from 'react';
import classNames from 'classnames';
import { similarity } from '@/utils/similarity';

interface TranscriptPopperProps {
  isVisible: boolean;
  transcript: string;
  className?: string;
  template?: string; // Template text để so sánh
}

interface ComparisonResult {
  highlightedTemplate: JSX.Element;
}

const TranscriptPopper: React.FC<TranscriptPopperProps> = ({
  isVisible,
  transcript,
  template,
  className
}) => {
  if (!isVisible) return null;

  // Function để so sánh transcript với template
  const compareTranscriptWithTemplate = useMemo((): ComparisonResult => {
    if (!template || !transcript) {
      return {
        highlightedTemplate: <span>{template || 'Không có template'}</span>
      };
    }

    // Tạo highlighted template với phrase-level comparison
    // Normalize từng từ trong template và transcript
    const normalizeWord = (word: string) => word.toLowerCase().replace(/[.,!?;:]/g, '');
    const words = template.split(' ');
    const normalizedWords = words.map(normalizeWord);
    const transcriptWords = transcript.trim().split(' ');
    const normalizedTranscriptWords = transcriptWords.map(normalizeWord);
    const transcriptWordCount = Math.min(normalizedTranscriptWords.length, normalizedWords.length);

    // So sánh phrase-by-phrase: lấy phần đầu của template tương ứng với độ dài transcript
    const templatePhrase = words.slice(0, transcriptWordCount).join(' ');
    const transcriptPhrase = transcriptWords.slice(0, transcriptWordCount).join(' ');

    // Normalize để so sánh
    const normalizePhrase = (phrase: string) =>
      phrase.toLowerCase()
        .replace(/[^\w\s]/g, '') // Loại bỏ dấu câu
        .replace(/\s+/g, ' ')    // Normalize spaces
        .trim();

    const normalizedTemplate = normalizePhrase(templatePhrase);
    const normalizedTranscript = normalizePhrase(transcriptPhrase);
    const phraseSimilarity = normalizedTemplate && normalizedTranscript
      ? similarity(normalizedTemplate, normalizedTranscript) / 100
      : 0;

    let phraseMatchLevel: 'low' | 'medium' | 'high';
    if (phraseSimilarity <= 0.5) {
      phraseMatchLevel = 'low';
    } else if (phraseSimilarity <= 0.8) {
      phraseMatchLevel = 'medium';
    } else {
      phraseMatchLevel = 'high';
    }
    console.log('[TranscriptPopper] phraseMatchLevel:', phraseMatchLevel);

    const highlightedTemplate = (
      <span>
        {words.map((word, index) => {
          // So sánh từng từ đã normalize
          const isHighlighted =  index < transcriptWordCount;

          return (
            <span
              key={index}
              className={classNames({
                'font-bold': isHighlighted,
                'text-green-500': isHighlighted && phraseMatchLevel === 'high',
                'text-yellow': isHighlighted && phraseMatchLevel === 'medium',
                'text-red': isHighlighted && phraseMatchLevel === 'low',
                'text-color-minor': !isHighlighted,
              })}
            >
              {word}
              {index < words.length - 1 ? ' ' : ''}
            </span>
          );
        })}
      </span>
    );

    return {
      highlightedTemplate
    };
  }, [template, transcript]);

  const { highlightedTemplate } = compareTranscriptWithTemplate;

  return (
    <div
      className={classNames(
        'absolute bottom-full mb-3 left-1/2 transform -translate-x-1/2 z-50',
        'bg-bg-box rounded-lg shadow-lg border border-color-border',
        'px-5 py-4 min-w-[400px] max-w-[500px]',
        'animate-in fade-in-0 zoom-in-95 duration-200',
        className
      )}
    >
      {/* Arrow pointing down */}
      <div className="absolute top-full left-1/2 transform -translate-x-1/2">
        <div className="w-0 h-0 border-l-[8px] border-r-[8px] border-t-[8px] border-l-transparent border-r-transparent border-t-bg-box"></div>
        <div className="absolute -top-[1px] left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[9px] border-r-[9px] border-t-[9px] border-l-transparent border-r-transparent border-t-bg-box"></div>
      </div>

      {/* Content */}
      <div className="text-sm leading-relaxed">
        {template ? (
          <div className="text-color-major leading-relaxed text-lg">
            {highlightedTemplate}
          </div>
        ) : (
          // Fallback khi không có template
          <div className="text-color-major">
            {transcript ? (
              <span>{transcript}</span>
            ) : (
              <span className="text-color-minor italic">Đang lắng nghe...</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TranscriptPopper;
