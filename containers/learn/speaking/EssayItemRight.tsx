'use client';

import React, { useEffect, useState } from 'react';

import NavigationModeEnum from '@/configs/NavigationModeEnum';
import { RecordProcessEnum } from '@/configs/RecordProcessEnum';
import classNames from 'classnames';
import Avatar from 'components/Avatar';
import { SentenceProcessEnum } from 'configs/SentenceProcessEnum';
import { map } from 'lodash';
import useLearnStore from 'store/learn';
import useSpeakingStore from 'store/speaking';
import { SpeakingItemProps, SpeakingItemRightProps } from 'types/component';
import { SentenceEntity } from 'types/model';

const EssayItem = ({
  speaking,
  position,
  sentenceLength,
  className,
  isLastRecord,
}: SpeakingItemProps) => {
  const { showWord, setSelectedSentence } = useLearnStore();
  const [scoreColor, setScoreColor] = useState<string>('');
  const { setSentence, sentenceScore, recordProcess } = useSpeakingStore();

  useEffect(() => {
    if (isLastRecord) {
      setSentence(speaking);
    } else {
      setScoreColor(speaking.color || '');
    }
  }, []);

  useEffect(() => {
    if (sentenceScore && sentenceScore.sentence_id === speaking.id) {
      setScoreColor(sentenceScore.color);
    }
  }, [sentenceScore]);

  const handleSpeakItem = () => {
    setSelectedSentence(speaking);
  };

  return (
    <div className={classNames('flex-0 relative !mt-0 mb-1 text-[0.9375rem]')}>
      <div
        onClick={() => setSelectedSentence(speaking)}
        className={classNames('px-1 cursor-pointer py-1 relative inline-block text-color-major', {
          hidden: isLastRecord && recordProcess !== RecordProcessEnum.FINISH,
          'text-red':
            scoreColor === 'red' &&
            (!isLastRecord || (isLastRecord && recordProcess === RecordProcessEnum.FINISH)),
          'text-primary':
            scoreColor === 'green' &&
            (!isLastRecord || (isLastRecord && recordProcess === RecordProcessEnum.FINISH)),
          'text-yellow':
            scoreColor === 'yellow' &&
            (!isLastRecord || (isLastRecord && recordProcess === RecordProcessEnum.FINISH)),
        })}
      >
        <span
          className={classNames(
            className,
            'transition-all ease-in-out duration-[1s] hover:!opacity-100',
            {
              '!opacity-100': showWord,
              '!opacity-5': isLastRecord && !showWord,
            }
          )}
        >
          {!isLastRecord || (isLastRecord && recordProcess === RecordProcessEnum.FINISH) ? (
            speaking.content
          ) : (
            <span className="opacity-0">{speaking.content}</span>
          )}
        </span>
      </div>
      {/* {!sentenceScore && (
            <div
              className={
                'w-6 h-6 ml-2 items-center cursor-pointer group-hover:flex justify-center rounded-full hover:bg-bg-box hidden absolute left-0'
              }
            >
              <span onClick={handleSpeakItem}>
                <i className={'text-medium text-color-minor icon-mic'} />
              </span>
            </div>
          )} */}
    </div>
  );
};

const EssayItemRight = ({
  sentences,
  activeCharacter,
  isLastItem,
  className,
  forwardMode,
}: SpeakingItemRightProps) => {
  const [rightSentences, setRightSentences] = useState<Array<SentenceEntity>>([]);
  const { recordProcess, setSentenceProcess, sentence, setSentence, setRecordProcess } =
    useSpeakingStore();
  const { currentSentenceId } = useLearnStore();

  useEffect(() => {
    if (!isLastItem) {
      setRightSentences([...sentences]);
    } else {
      if (recordProcess === RecordProcessEnum.INIT && forwardMode === NavigationModeEnum.NEXT) {
        handleListSentence();
      }
    }
  }, [recordProcess, forwardMode]);

  useEffect(() => {
    if (forwardMode === NavigationModeEnum.BACK && isLastItem) {
      const currentSentenceIndex = sentences.findIndex((item) => item.id === sentence?.id);
      if (currentSentenceIndex >= 0) {
        setRightSentences(sentences.slice(0, currentSentenceIndex + 1));
      }
    }
  }, [forwardMode, sentence]);

  const handleListSentence = () => {
    const lengthOfRightSentences = rightSentences.length || 0;
    if (lengthOfRightSentences >= sentences.length && forwardMode === NavigationModeEnum.NEXT) {
      setSentenceProcess(SentenceProcessEnum.FINISH);
      return;
    }

    let foundCurrentSentence = false;
    const existCurrentSentence = sentences.some((sentence) => sentence.id === currentSentenceId);
    if (existCurrentSentence && forwardMode === NavigationModeEnum.BACK) {
      setRightSentences([]);
      for (const sentenceItem of sentences) {
        setSentence(sentenceItem);
        setRightSentences((prevSentences) => [...prevSentences, sentenceItem]);
        if (sentenceItem.id === currentSentenceId) {
          foundCurrentSentence = true;
          setSentenceProcess(SentenceProcessEnum.PROCESS);
          break;
        }
      }
      if (!foundCurrentSentence) {
        setSentenceProcess(SentenceProcessEnum.FINISH);
      }
    } else {
      const nextSentence = sentences[lengthOfRightSentences] || null;
      console.log('right sentence ', nextSentence, sentences, lengthOfRightSentences);
      if (nextSentence) {
        setSentence(nextSentence);
        setRightSentences((prevState) => [...prevState, nextSentence]);
        setRecordProcess(RecordProcessEnum.PROCESS);
      }
    }
  };

  return (
    <div className={'flex items-start justify-start mb-0 text-[0.9375rem] mx-2'}>
      {rightSentences &&
        map(rightSentences, (speaking, index, array) => (
          <EssayItem
            key={speaking.id}
            speaking={speaking}
            isLastRecord={speaking.id === array[rightSentences.length - 1].id && isLastItem}
            position={index}
            className={className}
            sentenceLength={rightSentences.length}
          />
        ))}
    </div>
  );
};
export default EssayItemRight;
