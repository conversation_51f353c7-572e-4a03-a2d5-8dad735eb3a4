'use client';

import React from 'react';

import Link from 'next/link';

import FavouriteSkeleton from '@/containers/favourite/skeleton/FavouriteSkeleton';
import FavouriteButton from 'components/FavouriteButton';
import ScrollArea from 'components/ScrollArea';
import useFavouriteAll from 'hooks/Ent/useFavouriteAll';
import useFavourites from 'hooks/Ent/useFavourites';
import { map } from 'lodash';
import { useLocale } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';
import { typeStringToIconMap } from 'utils/common';
// import { format } from 'date-fns';
import { formatDateDay } from 'utils/datetime';

import NoContent from '@/components/NoContent';

const FavouriteListContainer = () => {
  const locale = useLocale();
  const { favouriteAll } = useFavouriteAll();
  const { favouriteList, isLoading, isReachingEnd, setPage, page } = useFavourites();

  if (!isLoading && !favouriteList.length)
    return (
      <table className="table-auto w-full">
        <thead>
          <tr className={''}>
            <th className={''}></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colSpan={3}>
              <NoContent />
            </td>
          </tr>
        </tbody>
      </table>
    );
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        dataLength={favouriteList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        {!favouriteList.length && !isLoading ? <NoContent /> : null}
        <table className="table-auto w-full">
          <tbody className={'text-[0.8123rem]'}>
            {favouriteList.length
              ? map(favouriteList, (favourite, key) => (
                  <tr
                    key={`tr-${key}`}
                    className="bg-bg-general hover:bg-bg-box/60 group border-b border-bg-box"
                  >
                    <td key={`td-1-${key}`} className={'p-2 pl-[30px] py-[10px] flex'}>
                      <Link
                        href={`/${
                          favourite.item === 'conversation' ||
                          favourite.item === 'essay' ||
                          favourite.item === 'gallery'
                            ? 'learn'
                            : favourite.item
                        }/${favourite?.paragraph?.keyx}`}
                      >
                        <div className="flex items-center gap-x-2 pt-[3px]">
                          <i
                            className={`text-[16px] ${
                              typeStringToIconMap[favourite?.item] || 'h-[13px] w-[13px]'
                            }`}
                          />
                          {favourite['item'] === 'conversation' ||
                          favourite['item'] === 'essay' ||
                          favourite['item'] === 'gallery'
                            ? favourite?.['paragraph']?.title
                            : favourite?.[favourite['item']]?.title}
                        </div>
                        <div className="text-color-minor text-[13px] ml-[31px]">
                          {favourite['item'] === 'conversation' ||
                          favourite['item'] === 'essay' ||
                          favourite['item'] === 'gallery'
                            ? favourite?.['paragraph']?.title_vi
                            : favourite?.[favourite['item']]?.title_vi}
                        </div>
                      </Link>
                      <div className="w-[39px] h-[29px]">
                        <FavouriteButton
                          favouriteList={favouriteAll}
                          item={favourite.item}
                          object_id={favourite.object_id}
                          className="ml-2"
                        />
                      </div>
                    </td>
                    <td key={`td-2-${key}`}>
                      {/* {format(new Date(favourite.updated_at * 1000), 'dd/MM/yyyy HH:mm')} */}
                      {favourite?.updated_at ? formatDateDay(favourite?.updated_at, locale) : ''}
                    </td>
                  </tr>
                ))
              : null}
            {isLoading ? <FavouriteSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};

// export default HomePage;

export default FavouriteListContainer;
