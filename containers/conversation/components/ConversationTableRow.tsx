import React from 'react';

import Link from 'next/link';

import { Button } from 'components';
import FavouriteButton from 'components/FavouriteButton';
import EntRouters from 'configs/EntRouters';
import { useTranslations } from 'next-intl';
import { FavouriteEntity, ParagraphEntity } from 'types/model';

interface ConversationTableRowProps {
  paragraph: ParagraphEntity;
  type: string;
  favouriteAll: Pick<FavouriteEntity, 'item' | 'object_id'>[];
}

const ConversationTableRow = ({ paragraph, type, favouriteAll }: ConversationTableRowProps) => {
  const t = useTranslations();

  return (
    <tr className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group [&>td>button]:hidden [&>td>button]:hover:flex">
      <td className={'py-[10px] flex pl-[30px] max-w-[600px]'}>
        <div>
          <div className={'flex items-center'}>
            <i className={`icon-${type} text-[16px] mr-2`} />
            <Link href={`${EntRouters.learn}/${paragraph.keyx}`}>{paragraph.title}</Link>
          </div>
          <div className="text-color-minor text-[13px] pl-[31px]">{paragraph.title_vi}</div>
        </div>
        <div className="w-[39px] h-[29px]">
          <FavouriteButton
            favouriteList={favouriteAll}
            item={type}
            object_id={paragraph?.id}
            className="ml-2"
          />
        </div>
      </td>

      <td className={'py-[10px]'}>
        <div>
          <div className={'flex items-center'}>{paragraph?.course?.title}</div>
          <div className="text-color-minor text-[13px]">{paragraph?.course?.title_vi}</div>
        </div>
      </td>

      <td className={'w-[120px] pr-3'}>
        {paragraph.status === 2 ? (
          <Button
            as={'a'}
            href={`${EntRouters.learn}/${paragraph.keyx}`}
            color={'primary'}
            size={'xs'}
            className={'group-hover:!flex hidden'}
          >
            {t('course.learnNow')}
          </Button>
        ) : null}
      </td>
    </tr>
  );
};

export default ConversationTableRow;
